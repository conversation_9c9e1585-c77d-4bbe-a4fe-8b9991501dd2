// swift-tools-version:6.0

import PackageDescription

// Swift 6.0 settings for macro support
let swiftSettings: [SwiftSetting] = [
  .enableExperimentalFeature("StrictConcurrency"),
  .enableUpcomingFeature("BareSlashRegexLiterals"),
  .enableUpcomingFeature("ConciseMagicFile"),
  .enableUpcomingFeature("ForwardTrailingClosures"),
  .enableUpcomingFeature("ImportObjcForwardDeclarations"),
  .enableUpcomingFeature("DisableOutwardActorInference"),
  .enableUpcomingFeature("ExistentialAny"),
  .enableUpcomingFeature("DeprecateApplicationMain"),
  .enableUpcomingFeature("GlobalConcurrency"),
  .enableUpcomingFeature("IsolatedDefaultValues"),
  .unsafeFlags([
    "-enable-experimental-feature", "AccessLevelOnImport",
    "-Xfrontend", "-enable-experimental-feature",
    "-Xfrontend", "SymbolLinkageMarkers",
    // Enable macro support for Swift 6.0
    "-enable-experimental-feature", "Macros"
  ]),
]

let package = Package(
  name: "Bridal",
  platforms: [
    .iOS(.v16),
    .macOS(.v13),
    .tvOS(.v16),
    .watchOS(.v9),
  ],
  products: [
    // MARK: - Core Application
    .library(name: "AppCore", targets: ["AppCore"]),
    .library(name: "AppSwiftUI", targets: ["AppSwiftUI"]),
    .executable(name: "BridalApp", targets: ["BridalApp"]),

    // MARK: - Main Tab Navigation
    .library(name: "MainTabCore", targets: ["MainTabCore"]),
    .library(name: "MainTabSwiftUI", targets: ["MainTabSwiftUI"]),

    // MARK: - New UI Flow Features
    .library(name: "LaunchCore", targets: ["LaunchCore"]),
    .library(name: "LaunchSwiftUI", targets: ["LaunchSwiftUI"]),
    .library(name: "ImageTypeSelectionCore", targets: ["ImageTypeSelectionCore"]),
    .library(name: "ImageTypeSelectionSwiftUI", targets: ["ImageTypeSelectionSwiftUI"]),
    .library(name: "ImageGenerationCore", targets: ["ImageGenerationCore"]),
    .library(name: "ImageGenerationSwiftUI", targets: ["ImageGenerationSwiftUI"]),
    .library(name: "ImageViewCore", targets: ["ImageViewCore"]),
    .library(name: "ImageViewSwiftUI", targets: ["ImageViewSwiftUI"]),

    // MARK: - Authentication
    .library(name: "AuthenticationClient", targets: ["AuthenticationClient"]),
    .library(name: "AuthenticationClientLive", targets: ["AuthenticationClientLive"]),
    .library(name: "LoginCore", targets: ["LoginCore"]),
    .library(name: "LoginSwiftUI", targets: ["LoginSwiftUI"]),
    .library(name: "TwoFactorCore", targets: ["TwoFactorCore"]),
    .library(name: "TwoFactorSwiftUI", targets: ["TwoFactorSwiftUI"]),

    // MARK: - Common Modules
    .library(name: "CommonUI", targets: ["CommonUI"]),
    .library(name: "LoggingClient", targets: ["LoggingClient"]),
    .library(name: "NetworkClient", targets: ["NetworkClient"]),
    .library(name: "NetworkClientLive", targets: ["NetworkClientLive"]),
    .library(name: "AuthenticationClient", targets: ["AuthenticationClient"]),
    .library(name: "AuthenticationClientLive", targets: ["AuthenticationClientLive"]),
    .library(name: "KeychainClient", targets: ["KeychainClient"]),
    .library(name: "UserStorageClient", targets: ["UserStorageClient"]),
    // .library(name: "StoreKitClientLive", targets: ["StoreKitClientLive"]),

    // MARK: - Bridal AI Features
    .library(name: "PhotoUploadCore", targets: ["PhotoUploadCore"]),
    .library(name: "PhotoUploadSwiftUI", targets: ["PhotoUploadSwiftUI"]),
    .library(name: "SubscriptionCore", targets: ["SubscriptionCore"]),
    .library(name: "SubscriptionSwiftUI", targets: ["SubscriptionSwiftUI"]),
    .library(name: "StyleSelectionCore", targets: ["StyleSelectionCore"]),
    .library(name: "StyleSelectionSwiftUI", targets: ["StyleSelectionSwiftUI"]),
    .library(name: "AIGenerationCore", targets: ["AIGenerationCore"]),
    .library(name: "AIGenerationSwiftUI", targets: ["AIGenerationSwiftUI"]),

    // MARK: - Quota Management
    .library(name: "QuotaClient", targets: ["QuotaClient"]),
    .library(name: "QuotaClientLive", targets: ["QuotaClientLive"]),
    .library(name: "ImageGenerationClient", targets: ["ImageGenerationClient"]),
    .library(name: "ImageGenerationClientLive", targets: ["ImageGenerationClientLive"]),
    .library(name: "ImageUploadClient", targets: ["ImageUploadClient"]),
    .library(name: "ImageUploadClientLive", targets: ["ImageUploadClientLive"]),
    .library(name: "NewImageGenerationClient", targets: ["NewImageGenerationClient"]),
    .library(name: "NewImageGenerationClientLive", targets: ["NewImageGenerationClientLive"]),
    .library(name: "QuotaExhaustedSwiftUI", targets: ["QuotaExhaustedSwiftUI"]),

    // MARK: - Feature Examples (Remove when creating new app)
    .library(name: "ProfileCore", targets: ["ProfileCore"]),
    .library(name: "ProfileSwiftUI", targets: ["ProfileSwiftUI"]),
    .library(name: "NotificationSettingsCore", targets: ["NotificationSettingsCore"]),
    .library(name: "NotificationSettingsSwiftUI", targets: ["NotificationSettingsSwiftUI"]),
    .library(name: "UserStateCore", targets: ["UserStateCore"]),
    .library(name: "UserStateSwiftUI", targets: ["UserStateSwiftUI"]),
  ],
  dependencies: [
    .package(url: "https://github.com/pointfreeco/swift-composable-architecture", from: "1.15.0"),
    .package(url: "https://github.com/pointfreeco/swift-dependencies", from: "1.4.0"),
    .package(url: "https://github.com/pointfreeco/swift-case-paths", from: "1.1.0"),
    .package(url: "https://github.com/pointfreeco/swift-perception", from: "1.6.0"),
    // Add other common dependencies here as needed
    // .package(url: "https://github.com/SwiftUIX/SwiftUIX.git", branch: "master"),
    // .package(url: "https://github.com/Alamofire/Alamofire", from: "5.0.0"),
    // .package(url: "https://github.com/onevcat/Kingfisher", from: "7.0.0"),
  ],
  targets: [
    // MARK: - Executable App
    .executableTarget(
      name: "BridalApp",
      dependencies: [
        "AppCore",
        "AppSwiftUI",
        "NetworkClientLive",
        // "StoreKitClientLive",
        "ImageUploadClientLive",
        "NewImageGenerationClientLive",
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
      ],
      path: "Sources/BridalApp",
      swiftSettings: swiftSettings
    ),

    // MARK: - New UI Flow Features
    .target(
      name: "LaunchCore",
      dependencies: [
        "LoggingClient",
        "AuthenticationClient",
        "UserStorageClient",
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
      ],
      swiftSettings: swiftSettings
    ),
    .target(
      name: "LaunchSwiftUI",
      dependencies: [
        "LaunchCore",
        "CommonUI",
      ]
    ),
    .target(
      name: "ImageTypeSelectionCore",
      dependencies: [
        "UserStateCore",
        "LoggingClient",
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
      ],
      swiftSettings: swiftSettings
    ),
    .target(
      name: "ImageTypeSelectionSwiftUI",
      dependencies: [
        "ImageTypeSelectionCore",
        "CommonUI",
        .product(name: "Perception", package: "swift-perception"),
      ],
      swiftSettings: swiftSettings
    ),
    .target(
      name: "ImageGenerationCore",
      dependencies: [
        "PhotoUploadCore",
        "ImageTypeSelectionCore",
        "ImageGenerationClient",
        "ImageUploadClient",
        "NewImageGenerationClient",
        "UserStateCore",
        "LoggingClient",
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
      ],
      swiftSettings: swiftSettings
    ),
    .target(
      name: "ImageGenerationSwiftUI",
      dependencies: [
        "ImageGenerationCore",
        "CommonUI",
      ]
    ),
    .target(
      name: "ImageViewCore",
      dependencies: [
        "ImageGenerationCore",
        "LoggingClient",
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
      ],
      swiftSettings: swiftSettings
    ),
    .target(
      name: "ImageViewSwiftUI",
      dependencies: [
        "ImageViewCore",
        "CommonUI",
        .product(name: "Perception", package: "swift-perception"),
      ],
      swiftSettings: swiftSettings
    ),

    // MARK: - Core Application
    .target(
      name: "AppCore",
      dependencies: [
        "AuthenticationClient",
        "LoginCore",
        "LaunchCore",
        "MainTabCore",
        "SubscriptionCore",
        "UserStateCore",
        "LoggingClient",
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
      ],
      swiftSettings: swiftSettings
    ),
    .testTarget(
      name: "AppCoreTests",
      dependencies: ["AppCore"]
    ),
    .target(
      name: "AppSwiftUI",
      dependencies: [
        "AppCore",
        "CommonUI",
        "LaunchSwiftUI",
        "LoginSwiftUI",
        "MainTabSwiftUI",
      ]
    ),

    // MARK: - Main Tab Navigation
    .target(
      name: "MainTabCore",
      dependencies: [
        "PhotoUploadCore",
        "ImageTypeSelectionCore",
        "ImageGenerationCore",
        "ImageViewCore",
        "ProfileCore",
        "SubscriptionCore",
        "UserStateCore",
        "LoggingClient",
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
      ],
      swiftSettings: swiftSettings
    ),
    .target(
      name: "MainTabSwiftUI",
      dependencies: [
        "MainTabCore",
        "PhotoUploadSwiftUI",
        "ImageTypeSelectionSwiftUI",
        "ImageGenerationSwiftUI",
        "ImageViewSwiftUI",
        "ProfileSwiftUI",
        "SubscriptionSwiftUI",
        "CommonUI",
        .product(name: "Perception", package: "swift-perception"),
      ],
      swiftSettings: swiftSettings
    ),

    // MARK: - Authentication
    .target(
      name: "AuthenticationClient",
      dependencies: [
        "CommonUI",
        "LoggingClient",
        "NetworkClient",
        .product(name: "Dependencies", package: "swift-dependencies"),
        .product(name: "DependenciesMacros", package: "swift-dependencies"),
      ],
      swiftSettings: swiftSettings,
      plugins: [
        .plugin(name: "DependenciesMacros", package: "swift-dependencies"),
      ]
    ),
    .target(
      name: "AuthenticationClientLive",
      dependencies: [
        "AuthenticationClient",
        "NetworkClient",
      ]
    ),
    // .target(
    //   name: "StoreKitClientLive",
    //   dependencies: [
    //     "SubscriptionCore",
    //     "UserStateCore",
    //     .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
    //   ],
    //   swiftSettings: swiftSettings
    // ),

    // MARK: - Common Modules
    .target(
      name: "CommonUI",
      dependencies: [
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture")
      ],
      swiftSettings: swiftSettings
    ),
    .target(
      name: "LoggingClient",
      dependencies: [
        .product(name: "Dependencies", package: "swift-dependencies"),
        .product(name: "DependenciesMacros", package: "swift-dependencies"),
      ],
      swiftSettings: swiftSettings,
      plugins: [
        .plugin(name: "DependenciesMacros", package: "swift-dependencies"),
      ]
    ),
    .target(
      name: "NetworkClient",
      dependencies: [
        .product(name: "Dependencies", package: "swift-dependencies"),
        .product(name: "DependenciesMacros", package: "swift-dependencies"),
      ],
      swiftSettings: swiftSettings,
      plugins: [
        .plugin(name: "DependenciesMacros", package: "swift-dependencies"),
      ]
    ),
    .target(
      name: "NetworkClientLive",
      dependencies: ["NetworkClient"]
    ),
    .target(
      name: "KeychainClient",
      dependencies: [
        .product(name: "Dependencies", package: "swift-dependencies"),
        .product(name: "DependenciesMacros", package: "swift-dependencies"),
      ],
      swiftSettings: swiftSettings,
      plugins: [
        .plugin(name: "DependenciesMacros", package: "swift-dependencies"),
      ]
    ),
    .target(
      name: "UserStorageClient",
      dependencies: [
        "KeychainClient",
        "UserStateCore",
        "AuthenticationClient",
        .product(name: "Dependencies", package: "swift-dependencies"),
        .product(name: "DependenciesMacros", package: "swift-dependencies"),
      ],
      swiftSettings: swiftSettings,
      plugins: [
        .plugin(name: "DependenciesMacros", package: "swift-dependencies"),
      ]
    ),

    .target(
      name: "LoginCore",
      dependencies: [
        "AuthenticationClient",
        "UserStorageClient",
        "UserStateCore",
        "TwoFactorCore",
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
        .product(name: "CasePaths", package: "swift-case-paths"),
      ],
      swiftSettings: swiftSettings,

    ),
    .testTarget(
      name: "LoginCoreTests",
      dependencies: ["LoginCore"]
    ),
    .target(
      name: "LoginSwiftUI",
      dependencies: [
        "LoginCore",
        "CommonUI",
        "TwoFactorSwiftUI",
        "UserStateSwiftUI",
        .product(name: "Perception", package: "swift-perception"),
      ],
      swiftSettings: swiftSettings
    ),

    // MARK: - Bridal AI Features
    .target(
      name: "PhotoUploadCore",
      dependencies: [
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
        .product(name: "Dependencies", package: "swift-dependencies"),
        .product(name: "DependenciesMacros", package: "swift-dependencies"),
        "CommonUI",
      ],
      swiftSettings: swiftSettings,
      plugins: [
        .plugin(name: "DependenciesMacros", package: "swift-dependencies"),
      ]
    ),
    .testTarget(
      name: "PhotoUploadCoreTests",
      dependencies: ["PhotoUploadCore"]
    ),
    .target(
      name: "PhotoUploadSwiftUI",
      dependencies: [
        "PhotoUploadCore",
        "CommonUI",
      ]
    ),

    .target(
      name: "SubscriptionCore",
      dependencies: [
        "UserStateCore",
        "LoggingClient",
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
        .product(name: "Dependencies", package: "swift-dependencies"),
        .product(name: "DependenciesMacros", package: "swift-dependencies"),
      ],
      swiftSettings: swiftSettings,
      plugins: [
        .plugin(name: "DependenciesMacros", package: "swift-dependencies"),
      ]
    ),
    .target(
      name: "SubscriptionSwiftUI",
      dependencies: [
        "SubscriptionCore",
        "CommonUI",
      ]
    ),

    .target(
      name: "StyleSelectionCore",
      dependencies: [
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
        .product(name: "Dependencies", package: "swift-dependencies"),
        .product(name: "DependenciesMacros", package: "swift-dependencies"),
      ],
      swiftSettings: swiftSettings,
      plugins: [
        .plugin(name: "DependenciesMacros", package: "swift-dependencies"),
      ]
    ),
    .testTarget(
      name: "StyleSelectionCoreTests",
      dependencies: ["StyleSelectionCore"]
    ),
    .target(
      name: "StyleSelectionSwiftUI",
      dependencies: [
        "StyleSelectionCore",
        "CommonUI",
      ]
    ),

    .target(
      name: "AIGenerationCore",
      dependencies: [
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
        .product(name: "Dependencies", package: "swift-dependencies"),
        .product(name: "DependenciesMacros", package: "swift-dependencies"),
      ],
      swiftSettings: swiftSettings,
      plugins: [
        .plugin(name: "DependenciesMacros", package: "swift-dependencies"),
      ]
    ),
    .testTarget(
      name: "AIGenerationCoreTests",
      dependencies: ["AIGenerationCore"]
    ),
    .target(
      name: "AIGenerationSwiftUI",
      dependencies: [
        "AIGenerationCore",
        "CommonUI",
      ]
    ),

    // MARK: - Feature Examples (Replace with your own features)
    .target(
      name: "ProfileCore",
      dependencies: [
        "AuthenticationClient",
        "UserStateCore",
        "UserStorageClient",
        "SubscriptionCore",
        "NotificationSettingsCore",
        "NetworkClient",
        "NetworkClientLive",
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
      ],
      swiftSettings: swiftSettings
    ),
    .testTarget(
      name: "ProfileCoreTests",
      dependencies: ["ProfileCore"]
    ),
    .target(
      name: "ProfileSwiftUI",
      dependencies: [
        "ProfileCore",
        "CommonUI",
        "NotificationSettingsSwiftUI",
      ]
    ),

    .target(
      name: "NotificationSettingsCore",
      dependencies: [
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
        .product(name: "Dependencies", package: "swift-dependencies"),
        .product(name: "DependenciesMacros", package: "swift-dependencies"),
      ],
      swiftSettings: swiftSettings,
      plugins: [
        .plugin(name: "DependenciesMacros", package: "swift-dependencies"),
      ]
    ),
    .target(
      name: "NotificationSettingsSwiftUI",
      dependencies: [
        "NotificationSettingsCore",
        "CommonUI",
        .product(name: "Perception", package: "swift-perception"),
      ],
      swiftSettings: swiftSettings
    ),

    .target(
      name: "TwoFactorCore",
      dependencies: [
        "AuthenticationClient",
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
        .product(name: "CasePaths", package: "swift-case-paths"),
      ],
      swiftSettings: swiftSettings,

    ),
    .testTarget(
      name: "TwoFactorCoreTests",
      dependencies: ["TwoFactorCore"]
    ),
    .testTarget(
      name: "LoginKeychainTests",
      dependencies: [
        "UserStateCore",
        "KeychainClient",
        "UserStorageClient",
        "AuthenticationClient"
      ]
    ),
    .testTarget(
      name: "NetworkClientFixTests",
      dependencies: [
        "AuthenticationClient",
        "NetworkClient",
        "NetworkClientLive",
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
      ]
    ),
    .target(
      name: "TwoFactorSwiftUI",
      dependencies: [
        "TwoFactorCore",
        "CommonUI",
        .product(name: "Perception", package: "swift-perception"),
      ],
      swiftSettings: swiftSettings
    ),

    // MARK: - Quota Management
    .target(
      name: "QuotaClient",
      dependencies: [
        .product(name: "Dependencies", package: "swift-dependencies"),
        .product(name: "DependenciesMacros", package: "swift-dependencies"),
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
      ],
      swiftSettings: swiftSettings,
      plugins: [
        .plugin(name: "DependenciesMacros", package: "swift-dependencies"),
      ]
    ),
    .target(
      name: "QuotaClientLive",
      dependencies: [
        "QuotaClient",
        "NetworkClient",
        "KeychainClient",
      ]
    ),
    .target(
      name: "ImageGenerationClient",
      dependencies: [
        "QuotaClient",
        .product(name: "Dependencies", package: "swift-dependencies"),
        .product(name: "DependenciesMacros", package: "swift-dependencies"),
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
      ],
      swiftSettings: swiftSettings,
      plugins: [
        .plugin(name: "DependenciesMacros", package: "swift-dependencies"),
      ]
    ),
    .target(
      name: "ImageGenerationClientLive",
      dependencies: [
        "ImageGenerationClient",
        "NetworkClient",
        "KeychainClient",
        "QuotaClient",
      ]
    ),
    .target(
      name: "ImageUploadClient",
      dependencies: [
        "NetworkClient",
        "AuthenticationClient",
        .product(name: "Dependencies", package: "swift-dependencies"),
        .product(name: "DependenciesMacros", package: "swift-dependencies"),
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
      ],
      swiftSettings: swiftSettings,
      plugins: [
        .plugin(name: "DependenciesMacros", package: "swift-dependencies"),
      ]
    ),
    .target(
      name: "ImageUploadClientLive",
      dependencies: [
        "ImageUploadClient",
        "NetworkClient",
        "NetworkClientLive",
        "AuthenticationClient",
      ]
    ),
    .target(
      name: "NewImageGenerationClient",
      dependencies: [
        "NetworkClient",
        "AuthenticationClient",
        .product(name: "Dependencies", package: "swift-dependencies"),
        .product(name: "DependenciesMacros", package: "swift-dependencies"),
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
      ],
      swiftSettings: swiftSettings,
      plugins: [
        .plugin(name: "DependenciesMacros", package: "swift-dependencies"),
      ]
    ),
    .target(
      name: "NewImageGenerationClientLive",
      dependencies: [
        "NewImageGenerationClient",
        "NetworkClient",
        "NetworkClientLive",
        "AuthenticationClient",
      ]
    ),
    .target(
      name: "QuotaExhaustedSwiftUI",
      dependencies: [
        "QuotaClient",
        "UserStateCore",
        "CommonUI",
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
      ],
      swiftSettings: swiftSettings
    ),

    // MARK: - User State Management
    .target(
      name: "UserStateCore",
      dependencies: [
        "AuthenticationClient",
        "QuotaClient",
        .product(name: "ComposableArchitecture", package: "swift-composable-architecture"),
      ],
      swiftSettings: swiftSettings
    ),

    .target(
      name: "UserStateSwiftUI",
      dependencies: [
        "UserStateCore",
        "CommonUI",
      ]
    ),
  ],
  swiftLanguageModes: [.v6]
)
