import SwiftUI
import ComposableArchitecture
import SubscriptionCore
import UserStateCore
import CommonUI
#if canImport(UIKit)
import UIKit
#endif

// MARK: - 🌸 新拟物风格订阅页面

public struct SubscriptionView: View {
  let store: StoreOf<Subscription>
  
  public init(store: StoreOf<Subscription>) {
    self.store = store
  }
  
  public var body: some View {
    WithPerceptionTracking {
      Group {
        if store.isLoading && store.subscriptionPlans.isEmpty {
          LoadingView(message: "正在加载订阅计划...")
        } else if let error = store.error {
          ErrorView(error: error) {
            store.send(.loadSubscriptionPlans)
          }
        } else {
          SubscriptionContentView(store: store)
        }
      }
      .navigationTitle("升级到 Pro")
      .neumorphicNavigationStyle()
      .neumorphicBackground()
      .toolbar {
        ToolbarItem(placement: .confirmationAction) {
          Button("完成") {
            store.send(.dismissButtonTapped)
          }
          .foregroundColor(.softPink)
        }
      }
      .onAppear {
        store.send(.onAppear)
      }
      .alert(
        "购买成功",
        isPresented: Binding(
          get: { store.showingSuccessAlert },
          set: { _ in store.send(.dismissSuccessAlert) }
        )
      ) {
        Button("太棒了！") {
          store.send(.dismissSuccessAlert)
        }
      } message: {
        if let message = store.successMessage {
          Text(message)
        }
      }
    }
  }
}

// MARK: - 内容视图

struct SubscriptionContentView: View {
  let store: StoreOf<Subscription>

  var body: some View {
    WithPerceptionTracking {
      ScrollView {
        VStack(spacing: NeumorphicDesign.largeSpacing) {
          // Header Section
          SubscriptionHeaderView()

          // Subscription Plans
          SubscriptionPlansView(store: store)

          // Features Comparison
          if let selectedPlan = store.selectedPlan {
            FeaturesView(plan: selectedPlan)
          }

          // Action Buttons
          ActionButtonsView(store: store)

          // Footer
          SubscriptionFooterView(store: store)

          Spacer(minLength: 40)
        }
        .padding(.horizontal, NeumorphicDesign.largePadding)
        .padding(.top, NeumorphicDesign.largePadding)
      }
    }
  }
}

// MARK: - 头部视图

struct SubscriptionHeaderView: View {
  var body: some View {
    ContentCard(style: .elevated) {
      VStack(spacing: NeumorphicDesign.largeSpacing) {
        // Premium Icon
        ZStack {
          Circle()
            .fill(
              LinearGradient(
                colors: [.white.opacity(0.9), .softBeige.opacity(0.8)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
            .frame(width: 80, height: 80)
            .shadow(color: .shadowDark.opacity(0.2), radius: 10, x: 6, y: 6)
            .shadow(color: .shadowLight, radius: 10, x: -6, y: -6)

          Image(systemName: "crown.fill")
            .font(.system(size: 32, weight: .bold))
            .foregroundStyle(
              LinearGradient(
                colors: [.warmOrange, .softPink],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
        }

        VStack(spacing: NeumorphicDesign.smallSpacing) {
          Text("解锁高级功能")
            .font(.system(size: 28, weight: .bold))
            .foregroundColor(.textPrimary)
            .multilineTextAlignment(.center)

          Text("获得无限制访问所有功能，将您的体验提升到新的水平")
            .font(.customBody)
            .foregroundColor(.textSecondary)
            .multilineTextAlignment(.center)
            .fixedSize(horizontal: false, vertical: true)
        }
      }
    }
  }
}

// MARK: - 订阅计划视图

struct SubscriptionPlansView: View {
  let store: StoreOf<Subscription>

  var body: some View {
    WithPerceptionTracking {
      VStack(spacing: NeumorphicDesign.mediumSpacing) {
        HStack {
          Text("选择您的计划")
            .font(.system(size: 20, weight: .bold))
            .foregroundColor(.textPrimary)
          Spacer()
        }

        ForEach(store.subscriptionPlans) { plan in
          SubscriptionPlanCard(
            plan: plan,
            isSelected: store.selectedPlan?.id == plan.id,
            onTap: { store.send(.planSelected(plan)) }
          )
        }
      }
    }
  }
}

// MARK: - 订阅计划卡片

struct SubscriptionPlanCard: View {
  let plan: SubscriptionPlan
  let isSelected: Bool
  let onTap: () -> Void

  var body: some View {
    Button(action: {
      // 添加触觉反馈
      #if canImport(UIKit)
      let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
      impactFeedback.impactOccurred()
      #endif
      onTap()
    }) {
      ZStack {
        // 🌸 增强的选中状态边框 - 提高真机上的可见性
        if isSelected {
          RoundedRectangle(cornerRadius: 20)
            .stroke(
              LinearGradient(
                colors: [.softPink, .warmOrange],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              ),
              lineWidth: 3
            )
            .background(
              RoundedRectangle(cornerRadius: 20)
                .stroke(Color.softPink.opacity(0.3), lineWidth: 6)
                .blur(radius: 2)
            )
        }

        ContentCard(style: isSelected ? .elevated : .soft) {
          ZStack {
            // 🌸 增强的选中状态背景 - 提高真机上的对比度
            if isSelected {
              RoundedRectangle(cornerRadius: 16)
                .fill(
                  LinearGradient(
                    colors: [
                      .softPink.opacity(0.12),
                      .warmOrange.opacity(0.12),
                      .white.opacity(0.98)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                  )
                )
            }

            // Recommended badge
            if plan.isRecommended {
              VStack {
                HStack {
                  Spacer()
                  Text("推荐")
                    .font(.customCaption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                      Capsule()
                        .fill(
                          LinearGradient(
                            colors: [.softPink, .warmOrange],
                            startPoint: .leading,
                            endPoint: .trailing
                          )
                        )
                    )
                    .offset(y: -10)
                }
                Spacer()
              }
            }

          VStack(alignment: .leading, spacing: NeumorphicDesign.mediumSpacing) {
            // Plan header
            HStack {
              // 🌸 选中状态指示器
              if isSelected {
                Image(systemName: "checkmark.circle.fill")
                  .font(.system(size: 20, weight: .bold))
                  .foregroundStyle(
                    LinearGradient(
                      colors: [.softPink, .warmOrange],
                      startPoint: .topLeading,
                      endPoint: .bottomTrailing
                    )
                  )
                  .transition(.scale.combined(with: .opacity))
              }

              VStack(alignment: .leading, spacing: 4) {
                Text(plan.name)
                  .font(.cardTitle)
                  .fontWeight(.bold)
                  .foregroundColor(isSelected ? .softPink : .textPrimary)

                Text(plan.description)
                  .font(.customBody)
                  .foregroundColor(.textSecondary)
              }

              Spacer()

              VStack(alignment: .trailing, spacing: 2) {
                if let originalPrice = plan.originalPrice {
                  Text(originalPrice)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.textSecondary.opacity(0.7))
                    .strikethrough(true, color: .textSecondary.opacity(0.7))
                }

                // 🌸 选中状态的价格高亮 - 移除单独的动画，统一使用整体动画
                Text(plan.price)
                  .font(.system(size: isSelected ? 22 : 20, weight: .bold))
                  .foregroundStyle(
                    isSelected ?
                    LinearGradient(
                      colors: [.softPink, .warmOrange],
                      startPoint: .topLeading,
                      endPoint: .bottomTrailing
                    ) :
                    LinearGradient(
                      colors: [.textPrimary, .textPrimary],
                      startPoint: .topLeading,
                      endPoint: .bottomTrailing
                    )
                  )

                Text(plan.duration)
                  .font(.customCaption)
                  .foregroundColor(isSelected ? .softPink : .textSecondary)
                  .fontWeight(isSelected ? .semibold : .regular)
              }
            }

            // Discount badge
            if let discount = plan.discountPercentage {
              HStack {
                Text("节省 \(discount)%")
                  .font(.customCaption)
                  .fontWeight(.semibold)
                  .foregroundColor(.successSoft)
                  .padding(.horizontal, 8)
                  .padding(.vertical, 4)
                  .background(
                    Capsule()
                      .fill(Color.successSoft.opacity(0.1))
                  )
                Spacer()
              }
            }

            // Key features (first 3)
            VStack(alignment: .leading, spacing: 6) {
              ForEach(Array(plan.features.prefix(3).enumerated()), id: \.offset) { _, feature in
                HStack(spacing: 8) {
                  Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.successSoft)
                    .font(.customCaption)

                  Text(feature)
                    .font(.customCaption)
                    .foregroundColor(.textSecondary)

                  Spacer()
                }
              }

              if plan.features.count > 3 {
                HStack(spacing: 8) {
                  Image(systemName: "plus.circle")
                    .foregroundColor(.softPink)
                    .font(.customCaption)

                  Text("还有 \(plan.features.count - 3) 项功能")
                    .font(.customCaption)
                    .foregroundColor(.textSecondary)

                  Spacer()
                }
              }
            }
            }
          }
        }
      }
    }
    .buttonStyle(PlainButtonStyle())
    .scaleEffect(isSelected ? 1.02 : 1.0)  // 减少缩放幅度，提高真机性能
    .shadow(
      color: isSelected ? .softPink.opacity(0.15) : .clear,  // 减少阴影透明度
      radius: isSelected ? 8 : 0,  // 减少阴影半径
      x: 0,
      y: isSelected ? 4 : 0  // 减少阴影偏移
    )
    // 🌸 统一使用单一动画配置，避免动画冲突
    .animation(.spring(response: 0.4, dampingFraction: 0.8, blendDuration: 0), value: isSelected)
  }
}

// MARK: - 功能列表视图

struct FeaturesView: View {
  let plan: SubscriptionPlan

  var body: some View {
    ContentCard(style: .soft) {
      VStack(alignment: .leading, spacing: NeumorphicDesign.mediumSpacing) {
        HStack {
          Image(systemName: "star.fill")
            .foregroundColor(.warmOrange)
          Text("包含功能")
            .font(.cardTitle)
            .fontWeight(.bold)
            .foregroundColor(.textPrimary)
          Spacer()
        }

        LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: NeumorphicDesign.smallSpacing) {
          ForEach(Array(plan.features.enumerated()), id: \.offset) { _, feature in
            HStack(spacing: 8) {
              Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.successSoft)
                .font(.customBody)

              Text(feature)
                .font(.customBody)
                .foregroundColor(.textPrimary)
                .fixedSize(horizontal: false, vertical: true)

              Spacer()
            }
          }
        }
      }
    }
  }
}

// MARK: - 操作按钮视图

struct ActionButtonsView: View {
  let store: StoreOf<Subscription>

  var body: some View {
    WithPerceptionTracking {
      VStack(spacing: NeumorphicDesign.mediumSpacing) {
        // Purchase Button with selected plan price
        PrimaryButton(
          title: {
            if store.isPurchasing {
              return "处理中..."
            } else if let selectedPlan = store.selectedPlan {
              return "开始免费试用 - \(selectedPlan.price)/\(selectedPlan.duration)"
            } else {
              return "选择一个计划"
            }
          }(),
          isLoading: store.isPurchasing,
          isDisabled: store.selectedPlan == nil,
          action: { store.send(.purchaseButtonTapped) }
        )

        // Restore Purchases Button
        Button(action: { store.send(.restorePurchasesButtonTapped) }) {
          HStack {
            if store.isLoading && !store.isPurchasing {
              ProgressView()
                .scaleEffect(0.8)
            }
            Text("恢复购买")
              .font(.customBodyMedium)
              .fontWeight(.medium)
          }
          .foregroundColor(.softPink)
          .frame(maxWidth: .infinity)
          .padding(.vertical, NeumorphicDesign.mediumPadding)
          .background(
            RoundedRectangle(cornerRadius: 12)
              .fill(Color.creamBackground)
              .shadow(color: .shadowDark.opacity(0.1), radius: 4, x: 2, y: 2)
              .shadow(color: .shadowLight, radius: 4, x: -2, y: -2)
          )
        }
        .disabled(store.isLoading)
      }
    }
  }
}

// MARK: - 页脚视图

struct SubscriptionFooterView: View {
  let store: StoreOf<Subscription>

  var body: some View {
    VStack(spacing: NeumorphicDesign.smallSpacing) {
      Text("• 随时取消 • 无承诺 • 安全支付")
        .font(.customCaption)
        .foregroundColor(.textSecondary)
        .multilineTextAlignment(.center)

      HStack(spacing: 16) {
        Button("服务条款") {
          // Handle terms
        }
        .foregroundColor(.softPink)
        .font(.customCaption)

        Text("•")
          .font(.customCaption)
          .foregroundColor(.textSecondary)

        Button("隐私政策") {
          // Handle privacy
        }
        .foregroundColor(.softPink)
        .font(.customCaption)
      }
    }
  }
}

// MARK: - 加载视图

struct LoadingView: View {
  let message: String

  var body: some View {
    VStack(spacing: NeumorphicDesign.largeSpacing) {
      ProgressView()
        .progressViewStyle(CircularProgressViewStyle(tint: .softPink))
        .scaleEffect(1.5)

      Text(message)
        .font(.customBody)
        .foregroundColor(.textPrimary)
        .multilineTextAlignment(.center)
    }
    .padding()
  }
}

// MARK: - 错误视图

struct ErrorView: View {
  let error: SubscriptionError
  let retryAction: () -> Void

  var body: some View {
    ContentCard(style: .soft) {
      VStack(spacing: NeumorphicDesign.largeSpacing) {
        Image(systemName: "exclamationmark.triangle.fill")
          .font(.system(size: 48))
          .foregroundColor(.warningSoft)

        Text("加载失败")
          .font(.cardTitle)
          .fontWeight(.bold)
          .foregroundColor(.textPrimary)

        Text(error.localizedDescription)
          .font(.customBody)
          .foregroundColor(.textSecondary)
          .multilineTextAlignment(.center)

        if let suggestion = error.recoverySuggestion {
          Text(suggestion)
            .font(.customCaption)
            .foregroundColor(.textSecondary)
            .multilineTextAlignment(.center)
        }

        PrimaryButton(
          title: "重试",
          action: retryAction
        )
      }
    }
    .padding()
  }
}

// MARK: - Previews

#Preview("Subscription View") {
  NavigationView {
    SubscriptionView(
      store: StoreOf<Subscription>(
        initialState: {
          var state = Subscription.State()
          state.subscriptionPlans = [
            SubscriptionPlan(
              id: "monthly",
              productID: "com.wenhaofree.bridal.sub_monthly_44",
              name: "月度 Pro",
              description: "适合入门用户",
              price: "¥28",
              duration: "每月",
              features: ["每月40次高清生成", "解锁所有AI风格", "优先生成队列"]
            ),
            SubscriptionPlan(
              id: "yearly",
              productID: "com.wenhaofree.bridal.sub_yearly_600",
              name: "年度 Pro",
              description: "最佳性价比选择",
              price: "¥128",
              originalPrice: "¥336",
              duration: "每年",
              features: ["每年600次高清生成", "包含月度版所有功能", "节省33%费用"],
              isRecommended: true,
              discountPercentage: 33
            )
          ]
          state.selectedPlan = state.subscriptionPlans.first { $0.isRecommended }
          return state
        }()
      ) {
        Subscription()
      }
    )
  }
}