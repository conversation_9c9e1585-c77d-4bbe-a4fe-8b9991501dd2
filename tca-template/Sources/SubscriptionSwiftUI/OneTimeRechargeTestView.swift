import SwiftUI
import ComposableArchitecture
import SubscriptionCore
import AuthenticationClient

// MARK: - 一次性充值功能测试视图

/// 用于测试一次性充值功能的独立视图
struct OneTimeRechargeTestView: View {
  @State private var isLoading = false
  @State private var resultMessage = ""
  @State private var showResult = false
  
  var body: some View {
    VStack(spacing: 24) {
      Text("一次性充值功能测试")
        .font(.title)
        .fontWeight(.bold)
        .padding()
      
      VStack(spacing: 16) {
        Text("测试说明")
          .font(.headline)
          .foregroundColor(.primary)
        
        Text("点击下方按钮测试一次性充值接口调用功能。\n确保已登录并有有效的认证token。")
          .font(.body)
          .foregroundColor(.secondary)
          .multilineTextAlignment(.center)
          .padding(.horizontal)
      }
      
      // 认证状态检查
      VStack(spacing: 12) {
        Text("认证状态")
          .font(.headline)
          .foregroundColor(.primary)
        
        HStack {
          Image(systemName: AccessTokenManager.hasValidToken() ? "checkmark.circle.fill" : "xmark.circle.fill")
            .foregroundColor(AccessTokenManager.hasValidToken() ? .green : .red)
          
          Text(AccessTokenManager.hasValidToken() ? "已认证" : "未认证")
            .font(.body)
            .foregroundColor(AccessTokenManager.hasValidToken() ? .green : .red)
        }
        
        if let authHeader = AccessTokenManager.getAuthorizationHeader() {
          Text("Token: \(authHeader.prefix(30))...")
            .font(.caption)
            .foregroundColor(.secondary)
        }
      }
      .padding()
      .background(
        RoundedRectangle(cornerRadius: 12)
          .fill(Color.gray.opacity(0.1))
      )
      
      // 测试按钮
      Button(action: testOneTimeRecharge) {
        HStack {
          if isLoading {
            ProgressView()
              .scaleEffect(0.8)
          }
          Text(isLoading ? "调用中..." : "测试一次性充值接口")
            .font(.headline)
        }
        .foregroundColor(.white)
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
          RoundedRectangle(cornerRadius: 12)
            .fill(isLoading ? Color.gray : Color.blue)
        )
      }
      .disabled(isLoading || !AccessTokenManager.hasValidToken())
      .padding(.horizontal)
      
      // 结果显示
      if showResult {
        VStack(spacing: 8) {
          Text("测试结果")
            .font(.headline)
            .foregroundColor(.primary)
          
          Text(resultMessage)
            .font(.body)
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
            .padding()
            .background(
              RoundedRectangle(cornerRadius: 8)
                .fill(Color.gray.opacity(0.1))
            )
        }
        .padding(.horizontal)
      }
      
      Spacer()
    }
    .padding()
    .navigationTitle("充值测试")
  }
  
  // MARK: - 测试方法
  
  private func testOneTimeRecharge() {
    isLoading = true
    showResult = false
    
    Task {
      do {
        // 调用一次性充值接口
        try await callOneTimeRechargeAPI()
        
        await MainActor.run {
          resultMessage = "✅ 充值接口调用成功！\n积分已增加到您的账户中。"
          showResult = true
          isLoading = false
        }
        
      } catch {
        await MainActor.run {
          resultMessage = "❌ 充值接口调用失败：\n\(error.localizedDescription)"
          showResult = true
          isLoading = false
        }
      }
    }
  }
}

// MARK: - 测试用的API调用函数

/// 测试用的一次性充值API调用函数
private func callOneTimeRechargeAPI() async throws {
  // 这里复制SubscriptionCore中的实现
  guard let authHeader = AccessTokenManager.getAuthorizationHeader() else {
    throw TestError.noAuth
  }
  
  print("💰 [测试] 开始调用一次性充值接口...")
  print("✅ [测试] 找到认证token: \(authHeader.prefix(30))...")
  
  guard let url = URL(string: "http://127.0.0.1:8000/api/v1/subscriptions/one-time-recharge") else {
    throw TestError.invalidURL
  }
  
  var request = URLRequest(url: url)
  request.httpMethod = "POST"
  request.setValue(authHeader, forHTTPHeaderField: "Authorization")
  request.setValue("BridalApp/1.0.0", forHTTPHeaderField: "User-Agent")
  request.setValue("*/*", forHTTPHeaderField: "Accept")
  request.setValue("application/json", forHTTPHeaderField: "Content-Type")
  
  print("🌐 [测试] 发送充值请求到: \(url.absoluteString)")
  
  let (data, response) = try await URLSession.shared.data(for: request)
  
  if let httpResponse = response as? HTTPURLResponse {
    print("📊 [测试] HTTP状态码: \(httpResponse.statusCode)")
    
    if httpResponse.statusCode >= 200 && httpResponse.statusCode < 300 {
      if let responseString = String(data: data, encoding: .utf8) {
        print("✅ [测试] 充值接口调用成功")
        print("📄 [测试] 响应内容: \(responseString)")
      }
    } else {
      let errorMessage = String(data: data, encoding: .utf8) ?? "未知错误"
      print("❌ [测试] 服务器返回错误: \(httpResponse.statusCode)")
      print("📄 [测试] 错误内容: \(errorMessage)")
      throw TestError.httpError(httpResponse.statusCode, errorMessage)
    }
  }
}

// MARK: - 测试错误类型

enum TestError: Error, LocalizedError {
  case noAuth
  case invalidURL
  case httpError(Int, String)
  
  var errorDescription: String? {
    switch self {
    case .noAuth:
      return "没有有效的认证token，请先登录"
    case .invalidURL:
      return "API URL配置错误"
    case .httpError(let code, let message):
      return "服务器错误 (\(code)): \(message)"
    }
  }
}

// MARK: - 预览

#Preview("一次性充值测试") {
  NavigationView {
    OneTimeRechargeTestView()
  }
}
