import Foundation
import ComposableArchitecture
import ImageGeneration<PERSON>lient
import NetworkClient
import Keychain<PERSON>lient
import QuotaClient

#if canImport(UIKit)
import UIKit
#endif

// MARK: - Live Implementation Registration

extension ImageGenerationClient {
  public static let liveValue = ImageGenerationClient(
    generateImage: { request in
      return try await LiveImageGenerationClient.shared.generateImage(request)
    },
    getGenerationStatus: { generationId in
      return try await LiveImageGenerationClient.shared.getGenerationStatus(generationId)
    },
    cancelGeneration: { generationId in
      return try await LiveImageGenerationClient.shared.cancelGeneration(generationId)
    },
    validateQuotaForGeneration: {
      return try await LiveImageGenerationClient.shared.validateQuotaForGeneration()
    }
  )
}

// MARK: - Live Implementation

@MainActor
public class LiveImageGenerationClient: Sendable {
  public static let shared = LiveImageGenerationClient()
  
  @Dependency(\.networkClient) var networkClient
  @Dependency(\.keychainClient) var keychainClient
  @Dependency(\.quotaClient) var quotaClient
  
  private var activeGenerations: [String: Date] = [:]
  
  private init() {}
  
  // MARK: - Public Methods
  
  public func generateImage(_ request: GenerateImageRequest) async throws -> GeneratedImageResponse {
    // First validate quota
    let quotaValidation = try await validateQuotaForGeneration()
    guard quotaValidation.canGenerate else {
      throw ImageGenerationError.quotaExhausted
    }
    
    // Convert image data to base64 for server transmission
    let base64ImageData = request.imageData.base64EncodedString()
    
    let serverRequest = ServerGenerateImageRequest(
      templateId: request.templateId,
      templateName: request.templateName,
      imageData: base64ImageData,
      styleType: request.styleType,
      deviceFingerprint: request.deviceFingerprint,
      requestId: request.requestId
    )
    
    let networkRequest = try await buildNetworkRequest(for: serverRequest, path: "/api/generate-image", method: .POST)
    
    do {
      let data = try await networkClient.request(networkRequest)
      let response = try JSONDecoder().decode(ServerGeneratedImageResponse.self, from: data)
      
      // Track active generation
      activeGenerations[response.generationId] = Date()
      
      // Convert server response to client response
      return GeneratedImageResponse(
        success: response.success,
        generationId: response.generationId,
        imageUrls: response.imageUrls,
        thumbnailUrls: response.thumbnailUrls,
        remainingQuota: response.remainingQuota,
        quotaRefreshDate: response.quotaRefreshDate,
        processingTime: response.processingTime,
        message: response.message
      )
      
    } catch let error as NetworkError {
      throw mapNetworkError(error)
    }
  }
  
  public func getGenerationStatus(_ generationId: String) async throws -> GenerationStatusResponse {
    let networkRequest = try await buildNetworkRequest(for: EmptyRequest(), path: "/api/generation-status/\(generationId)", method: .GET)
    
    do {
      let data = try await networkClient.request(networkRequest)
      let response = try JSONDecoder().decode(ServerGenerationStatusResponse.self, from: data)
      
      return GenerationStatusResponse(
        generationId: response.generationId,
        status: GenerationServerStatus(rawValue: response.status) ?? .failed,
        progress: response.progress,
        imageUrls: response.imageUrls,
        thumbnailUrls: response.thumbnailUrls,
        error: response.error,
        estimatedTimeRemaining: response.estimatedTimeRemaining
      )
      
    } catch let error as NetworkError {
      throw mapNetworkError(error)
    }
  }
  
  public func cancelGeneration(_ generationId: String) async throws -> Bool {
    let networkRequest = try await buildNetworkRequest(for: EmptyRequest(), path: "/api/cancel-generation/\(generationId)", method: .DELETE)
    
    do {
      let data = try await networkClient.request(networkRequest)
      let response = try JSONDecoder().decode(CancelGenerationResponse.self, from: data)
      
      // Remove from active generations
      activeGenerations.removeValue(forKey: generationId)
      
      return response.success
      
    } catch let error as NetworkError {
      throw mapNetworkError(error)
    }
  }
  
  public func validateQuotaForGeneration() async throws -> QuotaValidationResult {
    do {
      let quotaInfo = try await quotaClient.getCurrentQuota()
      
      if quotaInfo.canGenerate {
        return QuotaValidationResult(
          canGenerate: true,
          remainingQuota: quotaInfo.remainingQuota,
          reason: nil,
          suggestedAction: nil
        )
      } else {
        let suggestedAction: SuggestedAction = quotaInfo.userType == .guest ? .login : .upgrade
        
        return QuotaValidationResult(
          canGenerate: false,
          remainingQuota: quotaInfo.remainingQuota,
          reason: "Generation quota exhausted",
          suggestedAction: suggestedAction
        )
      }
      
    } catch {
      // If quota check fails, default to denying generation
      return QuotaValidationResult(
        canGenerate: false,
        remainingQuota: 0,
        reason: "Unable to verify quota: \(error.localizedDescription)",
        suggestedAction: .contactSupport
      )
    }
  }
  
  // MARK: - Private Methods
  
  private func buildNetworkRequest<T: Codable>(for requestBody: T, path: String, method: HTTPMethod) async throws -> NetworkRequest {
    guard let baseURL = URL(string: "https://bridal-api.wenhaofree.com") else {
      throw NetworkError.invalidURL("https://bridal-api.wenhaofree.com")
    }
    
    var headers = ["Content-Type": "application/json"]
    
    // Add authentication token if available
    if let tokenData = try? await keychainClient.load("auth_token"),
       let token = String(data: tokenData, encoding: .utf8) {
      headers["Authorization"] = "Bearer \(token)"
    }
    
    // Add device fingerprint for security
    let deviceFingerprint = generateDeviceFingerprint()
    headers["X-Device-Fingerprint"] = deviceFingerprint
    
    let requestBuilder = RequestBuilder(baseURL: baseURL)
      .path(path)
      .method(method)
      .headers(headers)
    
    if method != .GET && method != .DELETE {
      return try requestBuilder.body(requestBody).build()
    } else {
      return try requestBuilder.build()
    }
  }
  
  private func mapNetworkError(_ error: NetworkError) -> ImageGenerationError {
    switch error {
    case .noData:
      return .networkError("No data received")
    case .invalidResponse:
      return .networkError("Invalid response")
    case .decodingError(let message):
      return .networkError("Decoding error: \(message)")
    case .httpError(let statusCode, _):
      switch statusCode {
      case 401:
        return .unauthorized
      case 403:
        return .quotaExhausted
      case 413:
        return .imageTooLarge
      case 415:
        return .unsupportedFormat
      case 422:
        return .invalidImageData
      case 408, 504:
        return .requestTimeout
      default:
        return .serverError(statusCode, "HTTP error")
      }
    case .timeout:
      return .requestTimeout
    case .noInternetConnection:
      return .networkError("No internet connection")
    case .serverError(let message):
      return .serverError(500, message)
    case .unknown(let message):
      return .networkError(message)
    case .invalidURL, .encodingError:
      return .invalidRequest("Request error")
    }
  }
  
  private func generateDeviceFingerprint() -> String {
    let deviceId = UserDefaults.standard.string(forKey: "device_id") ?? UUID().uuidString
    let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
    
    #if canImport(UIKit)
    let systemVersion = UIDevice.current.systemVersion
    #else
    let systemVersion = "Unknown"
    #endif
    
    let timestamp = Date().timeIntervalSince1970
    
    let components = [deviceId, appVersion, systemVersion, String(Int(timestamp / 3600))]
    let combined = components.joined(separator: "|")
    
    return combined.data(using: .utf8)?.base64EncodedString() ?? deviceId
  }
}

// MARK: - Server API Models

private struct EmptyRequest: Codable {
  // Empty request for GET/DELETE endpoints
}

private struct ServerGenerateImageRequest: Codable {
  let templateId: String
  let templateName: String
  let imageData: String // Base64 encoded
  let styleType: String
  let deviceFingerprint: String
  let requestId: String
}

private struct ServerGeneratedImageResponse: Codable {
  let success: Bool
  let generationId: String
  let imageUrls: [String] // Changed to array for multiple images
  let thumbnailUrls: [String] // Changed to array for multiple thumbnails
  let remainingQuota: Int
  let quotaRefreshDate: Date?
  let processingTime: TimeInterval?
  let message: String?
}

private struct ServerGenerationStatusResponse: Codable {
  let generationId: String
  let status: String
  let progress: Double
  let imageUrls: [String] // Changed to array for multiple images
  let thumbnailUrls: [String] // Changed to array for multiple thumbnails
  let error: String?
  let estimatedTimeRemaining: TimeInterval?
}

private struct CancelGenerationResponse: Codable {
  let success: Bool
  let message: String?
}