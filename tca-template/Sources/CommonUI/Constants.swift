import Foundation

// MARK: - App Constants

public enum AppConstants {
  
  // MARK: - Authentication
  public enum Authentication {
    public static let mockTokenPrefix = "mock-email-token"
    public static let mock2FAToken = "mock-2fa-token"
    public static let mockAppleToken = "mock-apple-access-token"
    public static let mockAppleUserID = "mock-apple-user-456"
    public static let default2FAEmail = "<EMAIL>"
    public static let defaultAppleEmail = "<EMAIL>"
    public static let emailUserIDPrefix = "email_"
    public static let mock2FAUserID = "mock-2fa-user"
    public static let userIDLength = 8
  }
  
  // MARK: - UI Constants
  public enum UI {
    public static let buttonCornerRadius: CGFloat = 12
    public static let cardCornerRadius: CGFloat = 16
    public static let modernCardCornerRadius: CGFloat = 24
    public static let buttonPadding: CGFloat = 16
    public static let cardPadding: CGFloat = 20
    
    // Animation Durations
    public static let shortAnimationDuration: Double = 0.1
    public static let standardAnimationDuration: Double = 0.2
    public static let loadingAnimationDuration: Double = 1.0
    public static let rotationAnimationDuration: Double = 2.0
    
    // Scale Effects
    public static let pressedButtonScale: CGFloat = 0.95
    public static let pressedCardScale: CGFloat = 0.98
    public static let loadingIconScale: CGFloat = 1.2
    public static let progressViewScale: CGFloat = 0.8
    
    // Shadow Properties
    public static let elevatedShadowRadius: CGFloat = 8
    public static let gradientShadowRadius: CGFloat = 12
    public static let buttonShadowRadius: CGFloat = 10
    public static let pressedShadowRadius: CGFloat = 5
    
    // Line Widths
    public static let outlineWidth: CGFloat = 2
    public static let strokeWidth: CGFloat = 4
    public static let borderWidth: CGFloat = 1
  }
  
  // MARK: - User Defaults Keys
  public enum UserDefaults {
    public static let userAuthToken = "user_auth_token"
    public static let userEmail = "user_email"
    public static let userDisplayName = "user_display_name"
    public static let guestUsageCount = "guest_usage_count"
    public static let hasCompletedOnboarding = "has_completed_onboarding"
  }
  
  // MARK: - Network
  public enum Network {
    public static let requestTimeoutInterval: TimeInterval = 30.0
    public static let mockDelayShort: TimeInterval = 0.5
    public static let mockDelayMedium: TimeInterval = 1.0
    public static let mockDelayLong: TimeInterval = 2.0
  }
  
  // MARK: - Guest Usage Limits
  public enum GuestLimits {
    public static let maxImageGenerations = 3
    public static let maxStyleSelections = 5
    public static let maxPhotoUploads = 10
  }
  
  // MARK: - File Management
  public enum Files {
    public static let maxImageSizeMB = 10
    public static let supportedImageFormats = ["jpg", "jpeg", "png", "heic"]
    public static let maxImagesPerGeneration = 5
  }
  
  // MARK: - Validation
  public enum Validation {
    public static let minPasswordLength = 8
    public static let maxPasswordLength = 128
    public static let minEmailLength = 5
    public static let maxEmailLength = 254
    public static let maxDisplayNameLength = 50
  }
  
  // MARK: - App Info
  public enum App {
    public static let name = "Bridal AI"
    public static let bundleIdentifier = "com.bridal.app"
    public static let version = "1.0.0"
    public static let buildNumber = "1"
  }
  
  // MARK: - Localization Keys
  public enum LocalizationKeys {
    // Tab Titles
    public static let homeTab = "home_tab"
    public static let createTab = "create_tab"
    public static let galleryTab = "gallery_tab"
    public static let profileTab = "profile_tab"
    
    // Button Titles
    public static let getStarted = "get_started"
    public static let skipLogin = "skip_login"
    public static let login = "login"
    public static let logout = "logout"
    public static let tryAgain = "try_again"
    public static let cancel = "cancel"
    public static let confirm = "confirm"
    public static let save = "save"
    public static let delete = "delete"
    
    // Creation Flow
    public static let selectPhotos = "select_photos"
    public static let chooseStyle = "choose_style"
    public static let aiGeneration = "ai_generation"
    
    // Messages
    public static let loginSuccessful = "login_successful"
    public static let logoutCompleted = "logout_completed"
    public static let accountDeleted = "account_deleted"
    public static let somethingWentWrong = "something_went_wrong"
    public static let loading = "loading"
    public static let emptyState = "empty_state"
  }
}

// MARK: - Error Domain Constants

public enum ErrorDomains {
  public static let authentication = "com.bridal.app.authentication"
  public static let network = "com.bridal.app.network"
  public static let validation = "com.bridal.app.validation"
  public static let fileManagement = "com.bridal.app.filemanagement"
  public static let userState = "com.bridal.app.userstate"
}

// MARK: - Notification Names

public extension Notification.Name {
  static let userDidLogin = Notification.Name("userDidLogin")
  static let userDidLogout = Notification.Name("userDidLogout")
  static let authenticationFailed = Notification.Name("authenticationFailed")
  static let imageGenerationCompleted = Notification.Name("imageGenerationCompleted")
  static let subscriptionStatusChanged = Notification.Name("subscriptionStatusChanged")
}

// MARK: - API Endpoints (for future real implementation)

public enum APIEndpoints {
  // 基础配置
  public static let baseURL: String = {
    // 更精确的环境判断逻辑
    let url: String
    let environment: String

    #if DEBUG
      #if targetEnvironment(simulator)
        // 模拟器开发环境
        url = "http://127.0.0.1:8000"
        environment = "模拟器开发环境"
      #else
        // 真机开发环境 - 检查是否为TestFlight
        if isTestFlightBuild() {
          url = "https://bridal-api.wenhaofree.com"  // TestFlight使用生产环境
          environment = "TestFlight生产环境"
        } else {
          url = "http://***********:8000"  // 本地真机调试
          environment = "真机开发环境"
        }
      #endif
    #else
      // Release构建 - 生产环境
      url = "https://bridal-api.wenhaofree.com"
      environment = "Release生产环境"
    #endif

    print("🔧 [APIEndpoints] 使用\(environment)配置: \(url)")
    Task { @MainActor in
      LogManager.shared.info("API配置: \(environment) - \(url)", category: "Network")
    }
    return url
  }()

  // 认证相关
  public static let login = "/auth/login"
  public static let logout = "/auth/logout"
  public static let register = "/auth/register"
  public static let twoFactor = "/auth/2fa"
  public static let appleSignIn = "/auth/apple"

  // Apple OAuth 专用端点
  public static let appleOAuth = "/api/v1/oauth/apple/login"

  // AI 和图片相关
  public static let generateImage = "/ai/generate"
  public static let uploadPhoto = "/upload/photo"

  // 用户相关
  public static let getUserProfile = "/user/profile"
  public static let updateProfile = "/user/profile"
  public static let deleteAccount = "/user/delete"

  // 完整的 Apple OAuth URL
  public static var appleOAuthURL: String {
    return baseURL + appleOAuth
  }
  
  // 获取当前环境信息
  public static func getCurrentEnvironmentInfo() -> String {
    #if DEBUG
    #if targetEnvironment(simulator)
    return "DEBUG - iOS Simulator"
    #else
    return "DEBUG - Real Device"
    #endif
    #else
    return "RELEASE - Production"
    #endif
  }
  
  // 动态获取本机IP地址用于真实设备
  public static func getLocalIPAddress() -> String? {
    var address: String?
    var ifaddr: UnsafeMutablePointer<ifaddrs>?
    
    if getifaddrs(&ifaddr) == 0 {
      var ptr = ifaddr
      while ptr != nil {
        defer { ptr = ptr?.pointee.ifa_next }
        
        let interface = ptr?.pointee
        let addrFamily = interface?.ifa_addr.pointee.sa_family
        
        if addrFamily == UInt8(AF_INET) {
          let name = String(cString: (interface?.ifa_name)!)
          
          if name == "en0" || name == "en1" {  // WiFi interfaces
            var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
            let result = getnameinfo(interface?.ifa_addr, socklen_t((interface?.ifa_addr.pointee.sa_len)!), &hostname, socklen_t(hostname.count), nil, socklen_t(0), NI_NUMERICHOST)
            if result == 0 {
              address = String(cString: hostname)
              break
            }
          }
        }
      }
      freeifaddrs(ifaddr)
    }
    
    return address
  }
}

// MARK: - C函数导入
import Darwin

private func getifaddrs(_ ifap: UnsafeMutablePointer<UnsafeMutablePointer<ifaddrs>?>) -> Int32 {
  return Darwin.getifaddrs(ifap)
}

private func freeifaddrs(_ ifa: UnsafeMutablePointer<ifaddrs>?) {
  Darwin.freeifaddrs(ifa)
}

private func getnameinfo(_ sa: UnsafePointer<sockaddr>?, _ salen: socklen_t, _ host: UnsafeMutablePointer<CChar>?, _ hostlen: socklen_t, _ serv: UnsafeMutablePointer<CChar>?, _ servlen: socklen_t, _ flags: Int32) -> Int32 {
  return Darwin.getnameinfo(sa, salen, host, hostlen, serv, servlen, flags)
}

// MARK: - TestFlight Detection

/// 检测当前是否为TestFlight构建
private func isTestFlightBuild() -> Bool {
  #if targetEnvironment(simulator)
  return false  // 模拟器永远不是TestFlight
  #else
  // 检查是否存在TestFlight特有的receipt文件
  guard let receiptURL = Bundle.main.appStoreReceiptURL else {
    return false
  }

  // TestFlight构建的receipt路径包含"sandboxReceipt"
  let receiptPath = receiptURL.path
  let isTestFlight = receiptPath.contains("sandboxReceipt")

  print("🔍 [TestFlight检测] Receipt路径: \(receiptPath)")
  print("🔍 [TestFlight检测] 是否为TestFlight: \(isTestFlight)")

  return isTestFlight
  #endif
}