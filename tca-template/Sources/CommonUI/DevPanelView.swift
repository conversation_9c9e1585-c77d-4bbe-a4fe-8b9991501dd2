import SwiftUI
import Foundation
#if canImport(UIKit)
import UIKit
#endif

// MARK: - Developer Panel View

public struct DevPanelView: View {
  @ObservedObject private var logManager = LogManager.shared
  @Environment(\.dismiss) private var dismiss
  @State private var selectedLogLevel: LogLevel? = nil
  @State private var searchText = ""
  @State private var showingShareSheet = false
  @State private var showingClearAlert = false

  // 可选的store参数，用于API测试
  private let onTestAPIConnection: (() -> Void)?

  public init(onTestAPIConnection: (() -> Void)? = nil) {
    self.onTestAPIConnection = onTestAPIConnection
  }
  
  public var body: some View {
    NavigationView {
      VStack(spacing: 0) {
        // 搜索和过滤栏
        filterSection
        
        // 日志列表
        logListSection
        
        // 底部操作栏
        actionSection
      }
      .navigationTitle("开发者模式")
      #if os(iOS)
      .navigationBarTitleDisplayMode(.inline)
      #endif
      .toolbar {
        ToolbarItem(placement: {
          #if os(iOS)
          return .navigationBarLeading
          #else
          return .cancellationAction
          #endif
        }()) {
          Button("关闭") {
            dismiss()
          }
        }

        ToolbarItem(placement: {
          #if os(iOS)
          return .navigationBarTrailing
          #else
          return .primaryAction
          #endif
        }()) {
          Menu {
            if let testAction = onTestAPIConnection {
              Button(action: testAction) {
                Label("测试API连接", systemImage: "network")
              }
            }

            Button(action: { showingShareSheet = true }) {
              Label("导出日志", systemImage: "square.and.arrow.up")
            }

            Button(action: { showingClearAlert = true }) {
              Label("清空日志", systemImage: "trash")
            }
          } label: {
            Image(systemName: "ellipsis.circle")
          }
        }
      }
    }
    .alert("清空日志", isPresented: $showingClearAlert) {
      Button("取消", role: .cancel) { }
      Button("清空", role: .destructive) {
        logManager.clearLogs()
      }
    } message: {
      Text("确定要清空所有日志吗？此操作不可撤销。")
    }
    .sheet(isPresented: $showingShareSheet) {
      ShareSheet(items: [logManager.exportLogs()])
    }
  }
  
  // MARK: - Filter Section
  
  private var filterSection: some View {
    VStack(spacing: 12) {
      // 搜索框
      HStack {
        Image(systemName: "magnifyingglass")
          .foregroundColor(.secondary)
        
        TextField("搜索日志...", text: $searchText)
          .textFieldStyle(RoundedBorderTextFieldStyle())
      }
      
      // 日志级别过滤
      ScrollView(.horizontal, showsIndicators: false) {
        HStack(spacing: 8) {
          FilterChip(
            title: "全部",
            isSelected: selectedLogLevel == nil,
            action: { selectedLogLevel = nil }
          )
          
          ForEach(LogLevel.allCases, id: \.self) { level in
            FilterChip(
              title: level.rawValue.capitalized,
              isSelected: selectedLogLevel == level,
              color: level.color,
              action: { selectedLogLevel = selectedLogLevel == level ? nil : level }
            )
          }
        }
        .padding(.horizontal)
      }
    }
    .padding()
    .background({
      #if os(iOS)
      return Color(.systemGroupedBackground)
      #else
      return Color(.controlBackgroundColor)
      #endif
    }())
  }
  
  // MARK: - Log List Section
  
  private var logListSection: some View {
    List {
      ForEach(filteredLogs) { entry in
        LogEntryRow(entry: entry)
      }
    }
    .listStyle(PlainListStyle())
  }
  
  // MARK: - Action Section
  
  private var actionSection: some View {
    HStack(spacing: 16) {
      Button(action: { showingShareSheet = true }) {
        HStack {
          Image(systemName: "square.and.arrow.up")
          Text("导出")
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color.blue)
        .foregroundColor(.white)
        .cornerRadius(8)
      }
      
      Button(action: { showingClearAlert = true }) {
        HStack {
          Image(systemName: "trash")
          Text("清空")
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color.red)
        .foregroundColor(.white)
        .cornerRadius(8)
      }
    }
    .padding()
    .background({
      #if os(iOS)
      return Color(.systemGroupedBackground)
      #else
      return Color(.controlBackgroundColor)
      #endif
    }())
  }
  
  // MARK: - Computed Properties
  
  private var filteredLogs: [LogEntry] {
    var logs = logManager.logs
    
    // 按日志级别过滤
    if let selectedLevel = selectedLogLevel {
      logs = logs.filter { $0.level == selectedLevel }
    }
    
    // 按搜索文本过滤
    if !searchText.isEmpty {
      logs = logs.filter { entry in
        entry.message.localizedCaseInsensitiveContains(searchText) ||
        entry.category.localizedCaseInsensitiveContains(searchText)
      }
    }
    
    return logs.reversed() // 最新的在前面
  }
}

// MARK: - Log Entry Row

private struct LogEntryRow: View {
  let entry: LogEntry
  @State private var isExpanded = false
  
  var body: some View {
    VStack(alignment: .leading, spacing: 8) {
      // 头部信息
      HStack {
        Image(systemName: entry.level.icon)
          .foregroundColor(entry.level.color)
          .frame(width: 16)
        
        Text(entry.category)
          .font(.caption)
          .padding(.horizontal, 6)
          .padding(.vertical, 2)
          .background(entry.level.color.opacity(0.2))
          .cornerRadius(4)
        
        Spacer()
        
        Text(formatTime(entry.timestamp))
          .font(.caption)
          .foregroundColor(.secondary)
      }
      
      // 消息内容
      Text(entry.message)
        .font(.system(.caption, design: .monospaced))
        .lineLimit(isExpanded ? nil : 3)
        .onTapGesture {
          withAnimation(.easeInOut(duration: 0.2)) {
            isExpanded.toggle()
          }
        }
    }
    .padding(.vertical, 4)
  }
  
  private func formatTime(_ date: Date) -> String {
    let formatter = DateFormatter()
    formatter.dateFormat = "HH:mm:ss.SSS"
    return formatter.string(from: date)
  }
}

// MARK: - Filter Chip

private struct FilterChip: View {
  let title: String
  let isSelected: Bool
  let color: Color
  let action: () -> Void
  
  init(title: String, isSelected: Bool, color: Color = .blue, action: @escaping () -> Void) {
    self.title = title
    self.isSelected = isSelected
    self.color = color
    self.action = action
  }
  
  var body: some View {
    Button(action: action) {
      Text(title)
        .font(.caption)
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(isSelected ? color : {
          #if os(iOS)
          return Color(.systemGray5)
          #else
          return Color(.controlColor)
          #endif
        }())
        .foregroundColor(isSelected ? .white : .primary)
        .cornerRadius(16)
    }
  }
}

// MARK: - Share Sheet

#if canImport(UIKit)
private struct ShareSheet: UIViewControllerRepresentable {
  let items: [Any]
  
  func makeUIViewController(context: Context) -> UIActivityViewController {
    UIActivityViewController(activityItems: items, applicationActivities: nil)
  }
  
  func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}
#else
private struct ShareSheet: View {
  let items: [Any]
  
  var body: some View {
    Text("分享功能在此平台不可用")
      .padding()
  }
}
#endif
