import Foundation
import SwiftUI
#if canImport(UIKit)
import UIKit
#endif

// MARK: - Log Manager

@MainActor
public class LogManager: ObservableObject {
  public static let shared = LogManager()
  
  @Published private(set) var logs: [LogEntry] = []
  
  private init() {}
  
  public func log(_ message: String, level: LogLevel = .info, category: String = "General") {
    let entry = LogEntry(
      timestamp: Date(),
      level: level,
      category: category,
      message: message
    )
    
    logs.append(entry)
    
    // 限制日志数量，避免内存过大
    if logs.count > 1000 {
      logs.removeFirst()
    }
    
    // 在Debug模式下同时输出到控制台
    #if DEBUG
    print("[\(entry.level.rawValue.uppercased())] [\(category)] \(message)")
    #endif
  }
  
  public var allLogs: String {
    logs.map { entry in
      let formatter = DateFormatter()
      formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
      let timestamp = formatter.string(from: entry.timestamp)
      return "[\(timestamp)] [\(entry.level.rawValue.uppercased())] [\(entry.category)] \(entry.message)"
    }.joined(separator: "\n")
  }
  
  public func clearLogs() {
    logs.removeAll()
  }
  
  public func exportLogs() -> String {
    let header = """
    ==========================================
    App Logs Export
    Generated: \(Date())
    Total Entries: \(logs.count)
    ==========================================
    
    """
    return header + allLogs
  }
}

// MARK: - Log Entry

public struct LogEntry: Identifiable, Equatable {
  public let id = UUID()
  public let timestamp: Date
  public let level: LogLevel
  public let category: String
  public let message: String
}

// MARK: - Log Level

public enum LogLevel: String, CaseIterable {
  case debug = "debug"
  case info = "info"
  case warning = "warning"
  case error = "error"
  case critical = "critical"
  
  public var color: Color {
    switch self {
    case .debug:
      return .gray
    case .info:
      return .blue
    case .warning:
      return .orange
    case .error:
      return .red
    case .critical:
      return .purple
    }
  }
  
  public var icon: String {
    switch self {
    case .debug:
      return "ladybug"
    case .info:
      return "info.circle"
    case .warning:
      return "exclamationmark.triangle"
    case .error:
      return "xmark.circle"
    case .critical:
      return "exclamationmark.octagon"
    }
  }
}

// MARK: - Log Extensions

public extension LogManager {
  func debug(_ message: String, category: String = "Debug") {
    log(message, level: .debug, category: category)
  }
  
  func info(_ message: String, category: String = "Info") {
    log(message, level: .info, category: category)
  }
  
  func warning(_ message: String, category: String = "Warning") {
    log(message, level: .warning, category: category)
  }
  
  func error(_ message: String, category: String = "Error") {
    log(message, level: .error, category: category)
  }
  
  func critical(_ message: String, category: String = "Critical") {
    log(message, level: .critical, category: category)
  }
}

// MARK: - Network Logging Extension

public extension LogManager {
  func logNetworkRequest(url: String, method: String, headers: [String: String]? = nil) {
    var message = "🌐 \(method) \(url)"
    if let headers = headers, !headers.isEmpty {
      message += "\nHeaders: \(headers)"
    }
    log(message, level: .info, category: "Network")
  }
  
  func logNetworkResponse(url: String, statusCode: Int, responseTime: TimeInterval? = nil) {
    var message = "📡 Response: \(statusCode) - \(url)"
    if let responseTime = responseTime {
      message += " (\(String(format: "%.2f", responseTime))s)"
    }
    let level: LogLevel = statusCode >= 400 ? .error : .info
    log(message, level: level, category: "Network")
  }
  
  func logNetworkError(url: String, error: any Error) {
    log("❌ Network Error: \(url) - \(error.localizedDescription)", level: .error, category: "Network")
  }
}
