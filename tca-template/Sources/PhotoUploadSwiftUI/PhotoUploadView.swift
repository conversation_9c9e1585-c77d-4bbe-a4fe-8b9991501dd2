import SwiftUI
import ComposableArchitecture
import Perception
import PhotoUploadCore
import CommonUI
#if canImport(UIKit)
import UIKit
#endif

// MARK: - Photo Upload View

public struct PhotoUploadView: View {
  @Perception.Bindable var store: StoreOf<PhotoUpload>

  public init(store: StoreOf<PhotoUpload>) {
    self.store = store
  }
  
  public var body: some View {
    WithPerceptionTracking {
      GeometryReader { geometry in
        WithPerceptionTracking {
          VStack(spacing: 0) {
            // 主要内容区域
            VStack(spacing: NeumorphicDesign.mediumSpacing) {
              // 头部区域 - 简化版本
              if !store.hasSelectedPhotos {
                headerSection
              } else {
                compactHeaderSection
              }

              // 已选照片区域
              if store.hasSelectedPhotos {
                selectedPhotosSection
              }

              // 上传选项区域 - 紧凑版本
              if !store.hasSelectedPhotos {
                uploadOptionsSection
              } else {
                compactUploadOptionsSection
              }
            }
            .padding(.horizontal, NeumorphicDesign.largePadding)
            .padding(.top, NeumorphicDesign.smallPadding)

            Spacer()

            // 底部操作按钮区域 - 固定在底部
            if store.hasSelectedPhotos {
              actionButtonsSection
                .padding(.horizontal, NeumorphicDesign.largePadding)
                .padding(.bottom, geometry.safeAreaInsets.bottom + NeumorphicDesign.mediumPadding)
            }
          }
        }
      }
      .navigationTitle("选择照片")
      .neumorphicNavigationStyle()
      .neumorphicBackground()
      .toolbar {
        ToolbarItem(placement: .automatic) {
          WithPerceptionTracking {
            if store.hasSelectedPhotos {
              Button("清空") {
                store.send(.clearAllPhotos)
              }
              .foregroundColor(.red)
            }
          }
        }
      }
      .onAppear {
        store.send(.onAppear)
      }
      .alert(
        item: Binding(
          get: { store.error.map(ErrorWrapper.init) },
          set: { _ in store.send(.dismissError) }
        )
      ) { errorWrapper in
        Alert(
          title: Text("提示"),
          message: Text(errorWrapper.error.localizedDescription),
          primaryButton: .default(Text(buttonTextForError(errorWrapper.error))) {
            // Open settings if permission related
            #if os(iOS)
            if case .permissionDenied = errorWrapper.error,
               let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
              UIApplication.shared.open(settingsUrl)
            } else if case .cameraPermissionDenied = errorWrapper.error,
                      let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
              UIApplication.shared.open(settingsUrl)
            }
            #endif
          },
          secondaryButton: .cancel(Text("取消"))
        )
      }
      .sheet(isPresented: $store.isShowingImagePicker.sending(\.imagePickerPresented)) {
        ImagePickerView { images in
          print("📸 Images selected: \(images.count)")
          store.send(.photosSelected(images))
        }
      }
      .sheet(isPresented: $store.isShowingCamera.sending(\.cameraPresented)) {
        CameraView { image in
          if let image = image {
            store.send(.photosSelected([PhotoItem(image: image)]))
          }
        }
      }
    }
  }

  // MARK: - 🌸 新拟物风格头部区域
  
  private var headerSection: some View {
    VStack(spacing: NeumorphicDesign.largeSpacing) {
      // 新拟物风格图标
      ZStack {
        // 外圈新拟物效果
        Circle()
          .fill(
            LinearGradient(
              colors: [.white.opacity(0.9), .softBeige.opacity(0.8)],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .frame(width: 100, height: 100)
          .shadow(color: .shadowDark.opacity(0.2), radius: 12, x: 4, y: 4)
          .shadow(color: .shadowLight, radius: 12, x: -4, y: -4)

        // 主图标
        Image(systemName: "photo.on.rectangle.angled")
          .font(.system(size: 40, weight: .light))
          .foregroundStyle(
            LinearGradient(
              colors: [.softPink, .warmOrange],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
      }
      
      VStack(spacing: NeumorphicDesign.smallSpacing) {
        Text("上传您的照片")
          .font(.welcomeTitle)
          .fontWeight(.bold)
          .foregroundColor(.textPrimary)
        
        Text("选择1张照片，AI将为您生成精美的婚纱照")
          .font(.welcomeSubtitle)
          .foregroundColor(.textSecondary)
          .multilineTextAlignment(.center)
          .lineSpacing(4)
      }
    }
    .padding(.vertical, NeumorphicDesign.largePadding)
  }
  
  // MARK: - 🌸 紧凑版头部区域（有照片时显示）
  
  private var compactHeaderSection: some View {
    HStack(spacing: NeumorphicDesign.mediumSpacing) {
      // 小图标
      ZStack {
        Circle()
          .fill(
            LinearGradient(
              colors: [.white.opacity(0.9), .softBeige.opacity(0.8)],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .frame(width: 50, height: 50)
          .shadow(color: .shadowDark.opacity(0.15), radius: 6, x: 2, y: 2)
          .shadow(color: .shadowLight, radius: 6, x: -2, y: -2)

        Image(systemName: "photo.on.rectangle.angled")
          .font(.system(size: 20, weight: .medium))
          .foregroundStyle(
            LinearGradient(
              colors: [.softPink, .warmOrange],
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
      }
      
      VStack(alignment: .leading, spacing: 2) {
        Text("照片上传")
          .font(.cardTitle)
          .fontWeight(.semibold)
          .foregroundColor(.textPrimary)
        
        Text("点击重新选择或继续")
          .font(.customCaption)
          .foregroundColor(.textSecondary)
      }
      
      Spacer()
    }
    .padding(.vertical, NeumorphicDesign.smallPadding)
  }
  
  // MARK: - 🌸 新拟物风格已选照片区域
  
  private var selectedPhotosSection: some View {
    ContentCard(style: .soft) {
      VStack(alignment: .leading, spacing: NeumorphicDesign.smallSpacing) {
        HStack {
          Text("已选择照片")
            .font(.cardTitle)
            .foregroundColor(.textPrimary)

          Spacer()

          Text("\(store.selectedImages.count)/\(store.maxSelectionCount)")
            .font(.customCaption)
            .foregroundColor(.textSecondary)
            .padding(.horizontal, NeumorphicDesign.smallPadding)
            .padding(.vertical, 4)
            .background(
              RoundedRectangle(cornerRadius: NeumorphicDesign.smallRadius)
                .fill(Color.softPink.opacity(0.2))
            )
        }

        // 单张照片居中显示，1:1比例
        if let photo = store.selectedImages.first {
          HStack {
            Spacer()
            SelectedPhotoCard(photo: photo) {
              store.send(.removePhoto(photo))
            }
            Spacer()
          }
        }
      }
    }
  }
  
  // MARK: - 🌸 新拟物风格上传选项区域
  
  private var uploadOptionsSection: some View {
    VStack(spacing: NeumorphicDesign.mediumSpacing) {
      Text("照片上传方式")
        .font(.cardTitle)
        .foregroundColor(.textPrimary)
        .frame(maxWidth: .infinity, alignment: .leading)

      HStack(spacing: NeumorphicDesign.mediumSpacing) {
        UploadOptionCard(
          title: "相册",
          subtitle: "从相册选择",
          icon: "photo.on.rectangle",
          isEnabled: true
        ) {
          store.send(.selectFromPhotoLibrary)
        }

        UploadOptionCard(
          title: "相机",
          subtitle: "拍摄新照片",
          icon: "camera",
          isEnabled: true
        ) {
          store.send(.selectFromCamera)
        }
      }
    }
  }
  
  // MARK: - 🌸 紧凑版上传选项区域（有照片时显示）
  
  private var compactUploadOptionsSection: some View {
    HStack(spacing: NeumorphicDesign.smallSpacing) {
      Text("重新选择:")
        .font(.customBodyMedium)
        .foregroundColor(.textSecondary)

      // 相册按钮
      Button(action: { store.send(.selectFromPhotoLibrary) }) {
        HStack(spacing: 6) {
          Image(systemName: "photo.on.rectangle")
            .font(.system(size: 14, weight: .medium))
          Text("相册")
            .font(.customCaption)
        }
        .foregroundColor(.softPink)
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
          RoundedRectangle(cornerRadius: NeumorphicDesign.smallRadius)
            .fill(Color.white.opacity(0.8))
            .shadow(color: .shadowDark.opacity(0.1), radius: 2, x: 0, y: 1)
        )
      }

      // 相机按钮
      Button(action: { store.send(.selectFromCamera) }) {
        HStack(spacing: 6) {
          Image(systemName: "camera")
            .font(.system(size: 14, weight: .medium))
          Text("相机")
            .font(.customCaption)
        }
        .foregroundColor(.softPink)
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
          RoundedRectangle(cornerRadius: NeumorphicDesign.smallRadius)
            .fill(Color.white.opacity(0.8))
            .shadow(color: .shadowDark.opacity(0.1), radius: 2, x: 0, y: 1)
        )
      }

      Spacer()
    }
    .padding(.vertical, NeumorphicDesign.smallPadding)
  }
  
  // MARK: - 🌸 新拟物风格操作按钮区域
  
  private var actionButtonsSection: some View {
    VStack(spacing: NeumorphicDesign.smallSpacing) {
      // 加载状态指示器
      if store.isLoading {
        EnhancedLoadingView(message: "处理中...")
          .frame(height: 60)
      }

      // 下一步按钮
      PrimaryButton(
        title: "下一步：选择风格",
        style: .primary,
        isLoading: store.isLoading,
        isDisabled: !store.canProceedToNext
      ) {
        store.send(.proceedToStyleSelection)
      }
    }
    .padding(.top, NeumorphicDesign.smallPadding)
    .background(
      // 添加顶部分隔线效果
      VStack {
        Rectangle()
          .fill(Color.shadowInner.opacity(0.1))
          .frame(height: 1)
        Spacer()
      }
    )
  }

  // MARK: - Helper Functions

  private func buttonTextForError(_ error: PhotoUploadError) -> String {
    switch error {
    case .permissionDenied, .cameraPermissionDenied:
      return "设置"
    case .cameraNotAvailable:
      return "确定"
    default:
      return "确定"
    }
  }
}

// MARK: - Supporting Views

// MARK: - 🌸 新拟物风格照片卡片

struct SelectedPhotoCard: View {
  let photo: PhotoItem
  let onRemove: () -> Void
  
  @State private var isPressed = false

  var body: some View {
    ZStack(alignment: .topTrailing) {
      // 照片内容 - 1:1 比例显示
      photoImage
        .resizable()
        .aspectRatio(1, contentMode: .fill) // 强制1:1比例
        .frame(width: 150, height: 150) // 固定尺寸，确保1:1
        .clipped()
        .background(
          RoundedRectangle(cornerRadius: NeumorphicDesign.mediumRadius)
            .fill(Color.softBeige.opacity(0.3))
        )
        .cornerRadius(NeumorphicDesign.mediumRadius)
        .shadow(
          color: .shadowDark.opacity(0.15),
          radius: 8,
          x: 0,
          y: 4
        )
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
      
      // 删除按钮
      Button(action: onRemove) {
        ZStack {
          Circle()
            .fill(
              LinearGradient(
                colors: [.errorSoft.opacity(0.9), .errorSoft.opacity(0.7)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
            .frame(width: 24, height: 24)
            .shadow(color: .errorSoft.opacity(0.3), radius: 3, x: 0, y: 1)
          
          Image(systemName: "xmark")
            .font(.system(size: 12, weight: .bold))
            .foregroundColor(.white)
        }
      }
      .padding(6)
      .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
          isPressed = pressing
        }
      }, perform: {})
    }
  }

  private var photoImage: Image {
    #if canImport(UIKit)
    return Image(uiImage: photo.image)
    #else
    return Image(nsImage: photo.image)
    #endif
  }
}

// MARK: - 🌸 新拟物风格上传选项卡片

struct UploadOptionCard: View {
  let title: String
  let subtitle: String
  let icon: String
  let isEnabled: Bool
  let action: () -> Void
  
  @State private var isPressed = false
  
  var body: some View {
    Button(action: {
      #if canImport(UIKit)
      let impactFeedback = UIImpactFeedbackGenerator(style: .light)
      impactFeedback.impactOccurred()
      #endif
      action()
    }) {
      VStack(spacing: NeumorphicDesign.smallSpacing) {
        // 图标背景 - 新拟物效果
        ZStack {
          Circle()
            .fill(
              LinearGradient(
                colors: isEnabled 
                  ? [.softPink.opacity(0.2), .warmOrange.opacity(0.2)]
                  : [.lightGray.opacity(0.3), .lightGray.opacity(0.1)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
            .frame(width: 50, height: 50)
            .shadow(
              color: isEnabled ? .softPink.opacity(0.2) : .shadowDark.opacity(0.1),
              radius: 4,
              x: 0,
              y: 2
            )
          
          Image(systemName: icon)
            .font(.system(size: 24, weight: .medium))
            .foregroundStyle(
              isEnabled 
                ? LinearGradient(
                    colors: [.softPink, .warmOrange],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                  )
                : LinearGradient(
                    colors: [.textTertiary, .textTertiary],
                    startPoint: .top,
                    endPoint: .bottom
                  )
            )
        }
        
        VStack(spacing: 4) {
          Text(title)
            .font(.customBodyMedium)
            .foregroundColor(isEnabled ? .textPrimary : .textTertiary)
          
          Text(subtitle)
            .font(.customCaption)
            .foregroundColor(.textSecondary)
            .multilineTextAlignment(.center)
        }
      }
      .frame(maxWidth: .infinity)
      .padding(.vertical, NeumorphicDesign.largePadding)
      .background(
        ZStack {
          // 新拟物背景
          RoundedRectangle(cornerRadius: NeumorphicDesign.mediumRadius)
            .fill(
              LinearGradient(
                colors: [.white.opacity(0.9), .softBeige.opacity(0.8)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
          
          // 内阴影效果
          RoundedRectangle(cornerRadius: NeumorphicDesign.mediumRadius)
            .stroke(Color.white.opacity(0.8), lineWidth: 1)
            .blur(radius: 0.5)
            .offset(x: -1, y: -1)
          
          RoundedRectangle(cornerRadius: NeumorphicDesign.mediumRadius)
            .stroke(Color.shadowInner, lineWidth: 1)
            .blur(radius: 0.5)
            .offset(x: 1, y: 1)
        }
      )
      .scaleEffect(isPressed ? 0.98 : 1.0)
      .opacity(isEnabled ? 1.0 : 0.6)
      .shadow(
        color: .shadowDark.opacity(0.1),
        radius: isPressed ? 4 : 8,
        x: 0,
        y: isPressed ? 2 : 4
      )
    }
    .disabled(!isEnabled)
    .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
      withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
        isPressed = pressing
      }
    }, perform: {})
  }
}

// MARK: - Helper Views

#if os(iOS)
struct ImagePickerView: UIViewControllerRepresentable {
  let onImagesPicked: ([PhotoItem]) -> Void
  
  func makeUIViewController(context: Context) -> UIImagePickerController {
    let picker = UIImagePickerController()
    picker.delegate = context.coordinator
    picker.sourceType = .photoLibrary
    picker.allowsEditing = false
    return picker
  }
  
  func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
  
  func makeCoordinator() -> Coordinator {
    Coordinator(self)
  }
  
  class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    let parent: ImagePickerView
    
    init(_ parent: ImagePickerView) {
      self.parent = parent
    }
    
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
      if let image = info[.originalImage] as? UIImage {
        let photoItem = PhotoItem(image: image)
        // Dismiss first, then call the callback to avoid state conflicts
        picker.dismiss(animated: true) {
          self.parent.onImagesPicked([photoItem])
        }
      } else {
        picker.dismiss(animated: true)
      }
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
      picker.dismiss(animated: true)
    }
  }
}
#else
// macOS placeholder implementation
struct ImagePickerView: View {
  let onImagesPicked: ([PhotoItem]) -> Void

  var body: some View {
    VStack {
      Text("图片选择功能在 macOS 上暂不可用")
        .foregroundColor(.secondary)

      Button("模拟选择图片") {
        // Create mock photo for macOS testing
        let mockPhoto = PhotoItem(image: NSImage(systemSymbolName: "photo", accessibilityDescription: nil)!)
        onImagesPicked([mockPhoto])
      }
    }
    .padding()
  }
}
#endif

#if os(iOS)
struct CameraView: UIViewControllerRepresentable {
  let onImageCaptured: (UIImage?) -> Void

  func makeUIViewController(context: Context) -> UIViewController {
    // Check if camera is available before creating the picker
    guard UIImagePickerController.isSourceTypeAvailable(.camera) else {
      // Return a view controller that shows an error message
      let alertController = UIAlertController(
        title: "相机不可用",
        message: "当前设备不支持相机功能，请使用真机测试相机功能。",
        preferredStyle: .alert
      )

      alertController.addAction(UIAlertAction(title: "确定", style: .default) { _ in
        self.onImageCaptured(nil)
      })

      return alertController
    }

    let picker = UIImagePickerController()
    picker.delegate = context.coordinator
    picker.sourceType = .camera
    picker.allowsEditing = false
    return picker
  }

  func updateUIViewController(_ uiViewController: UIViewController, context: Context) {}

  func makeCoordinator() -> Coordinator {
    Coordinator(self)
  }

  class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    let parent: CameraView

    init(_ parent: CameraView) {
      self.parent = parent
    }

    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
      let image = info[.originalImage] as? UIImage
      // Dismiss first, then call the callback to avoid state conflicts
      picker.dismiss(animated: true) {
        self.parent.onImageCaptured(image)
      }
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
      picker.dismiss(animated: true) {
        self.parent.onImageCaptured(nil)
      }
    }
  }
}
#else
// macOS placeholder implementation
struct CameraView: View {
  let onImageCaptured: (NSImage?) -> Void

  var body: some View {
    VStack {
      Text("相机功能在 macOS 上暂不可用")
        .foregroundColor(.secondary)

      Button("模拟拍照") {
        // Create mock photo for macOS testing
        let mockPhoto = NSImage(systemSymbolName: "camera", accessibilityDescription: nil)
        onImageCaptured(mockPhoto)
      }
    }
    .padding()
  }
}
#endif

// MARK: - Error Wrapper

struct ErrorWrapper: Identifiable {
  let id = UUID()
  let error: PhotoUploadError
}

// MARK: - Previews

#Preview("Empty State") {
  PhotoUploadView(
    store: Store(initialState: PhotoUpload.State()) {
      PhotoUpload()
    }
  )
}

#Preview("With Photos") {
  let mockPhotos: [PhotoItem] = {
    #if canImport(UIKit)
    return [
      PhotoItem(image: UIImage(systemName: "photo")!),
      PhotoItem(image: UIImage(systemName: "photo.fill")!)
    ]
    #else
    return [
      PhotoItem(image: NSImage(systemSymbolName: "photo", accessibilityDescription: nil)!),
      PhotoItem(image: NSImage(systemSymbolName: "photo.fill", accessibilityDescription: nil)!)
    ]
    #endif
  }()

  PhotoUploadView(
    store: Store(
      initialState: PhotoUpload.State(selectedImages: mockPhotos)
    ) {
      PhotoUpload()
    }
  )
}
