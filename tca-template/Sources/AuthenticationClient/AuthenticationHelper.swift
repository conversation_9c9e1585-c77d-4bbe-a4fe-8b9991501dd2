import Foundation
import NetworkClient

// MARK: - 认证助手扩展

public extension RequestBuilder {
  /// 自动添加认证头（如果有可用的token）
  func autoAuth() -> Self {
    if let authHeader = AccessTokenManager.getAuthorizationHeader() {
      return header("Authorization", authHeader)
    }
    return self
  }
  
  /// 添加Apple认证头
  func appleAuth() -> Self {
    if let token = AccessTokenManager.getAppleAccessToken(),
       let tokenType = AccessTokenManager.getAppleTokenType() {
      return header("Authorization", "\(tokenType) \(token)")
    }
    return self
  }
  
  /// 添加Keychain认证头
  func keychainAuth() -> Self {
    if let token = AccessTokenManager.getKeychainAccessToken() {
      return header("Authorization", "bearer \(token)")
    }
    return self
  }
}

// MARK: - 认证助手

public struct AuthHelper {
  /// 检查是否有有效的认证token
  public static func hasValidAuth() -> Bool {
    return AccessTokenManager.hasValidToken()
  }
  
  /// 获取当前的认证头
  public static func getCurrentAuthHeader() -> String? {
    return AccessTokenManager.getAuthorizationHeader()
  }
  
  /// 打印当前认证状态（用于调试）
  public static func printAuthStatus() {
    print("🔐 认证状态检查:")
    
    if let appleToken = AccessTokenManager.getAppleAccessToken() {
      print("   ✅ Apple Token (UserDefaults): \(appleToken.prefix(20))...")
    } else {
      print("   ❌ Apple Token (UserDefaults): 未找到")
    }
    
    if let keychainToken = AccessTokenManager.getKeychainAccessToken() {
      print("   ✅ Keychain Token: \(keychainToken.prefix(20))...")
    } else {
      print("   ❌ Keychain Token: 未找到")
    }
    
    if let authHeader = AccessTokenManager.getAuthorizationHeader() {
      print("   ✅ Authorization Header: \(authHeader.prefix(30))...")
    } else {
      print("   ❌ Authorization Header: 未找到")
    }
    
    print("   📊 总体状态: \(AccessTokenManager.hasValidToken() ? "有效" : "无效")")
  }
}

// MARK: - 网络请求示例

public struct AuthenticatedAPIClient {
  private let networkClient: NetworkClient
  private let baseURL: URL
  
  public init(networkClient: NetworkClient, baseURL: URL) {
    self.networkClient = networkClient
    self.baseURL = baseURL
  }
  
  /// 示例：调用需要认证的用户信息API
  public func getUserProfile() async throws -> Data {
    let request = try RequestBuilder(baseURL: baseURL)
      .path("/api/v1/user/profile")
      .method(.GET)
      .contentTypeJSON()
      .acceptJSON()
      .autoAuth()  // 自动添加认证头
      .build()
    
    return try await networkClient.request(request)
  }
  
  /// 示例：调用需要认证的图片生成API
  public func generateImage(prompt: String) async throws -> Data {
    let requestBody: [String: Any] = [
      "prompt": prompt,
      "style": "romantic",
      "count": 1
    ]

    let jsonData = try JSONSerialization.data(withJSONObject: requestBody)

    let request = try RequestBuilder(baseURL: baseURL)
      .path("/api/v1/images/generate")
      .method(.POST)
      .contentTypeJSON()
      .acceptJSON()
      .autoAuth()  // 自动添加认证头
      .body(jsonData, contentType: "application/json")
      .build()
    
    return try await networkClient.request(request)
  }
  
  /// 示例：调用需要认证的配额查询API
  public func getQuotaStatus() async throws -> Data {
    let request = try RequestBuilder(baseURL: baseURL)
      .path("/api/v1/user/quota")
      .method(.GET)
      .contentTypeJSON()
      .acceptJSON()
      .autoAuth()  // 自动添加认证头
      .build()
    
    return try await networkClient.request(request)
  }
}

// MARK: - 使用示例

/*
使用示例：

// 1. 检查认证状态
AuthHelper.printAuthStatus()

// 2. 创建认证客户端
let apiClient = AuthenticatedAPIClient(
  networkClient: networkClient,
  baseURL: URL(string: "https://bridal-api.wenhaofree.com")!
)

// 3. 调用需要认证的API
do {
  let profileData = try await apiClient.getUserProfile()
  print("✅ 用户信息获取成功")
} catch {
  print("❌ 用户信息获取失败: \(error)")
}

// 4. 手动构建认证请求
let request = try RequestBuilder(baseURL: baseURL)
  .path("/api/v1/custom/endpoint")
  .method(.POST)
  .autoAuth()  // 自动添加认证头
  .contentTypeJSON()
  .body(requestData)
  .build()

let response = try await networkClient.request(request)
*/
