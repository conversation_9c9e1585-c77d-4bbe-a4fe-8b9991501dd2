import ComposableArchitecture
import AuthenticationClient
import UserStateCore
import UserStorageClient
import SubscriptionCore
import NotificationSettingsCore
import NetworkClient
import NetworkClientLive
import KeychainClient
import Foundation
import CommonUI

// MARK: - User Profile API Models

public struct UserProfileResponse: Codable, Equatable, Sendable {
  public let email: String
  public let isActive: Bool
  public let isSuperuser: Bool
  public let fullName: String
  public let platform: String
  public let createdAt: String
  public let lastLogin: String
  public let authProvider: String
  public let providerUserId: String
  public let avatarUrl: String?
  public let id: String
  public let totalCredits: Int
  public let remainingCredits: Int

  enum CodingKeys: String, CodingKey {
    case email
    case isActive = "is_active"
    case isSuperuser = "is_superuser"
    case fullName = "full_name"
    case platform
    case createdAt = "created_at"
    case lastLogin = "last_login"
    case authProvider = "auth_provider"
    case providerUserId = "provider_user_id"
    case avatarUrl = "avatar_url"
    case id
    case totalCredits = "total_credits"
    case remainingCredits = "remaining_credits"
  }
}

@Reducer
public struct Profile: Sendable {
  @ObservableState
  public struct State: Equatable, Sendable {
    public var user: UserStateCore.User?
    public var userProfile: UserProfileResponse?
    public var isLoading = false
    public var isLoadingProfile = false
    public var error: ProfileError?
    public var isEditingProfile = false
    public var editableUser: EditableUser?
    public var isShowingDeleteAccountAlert = false
    public var isShowingDeleteSuccessAlert = false
    public var isShowingSubscriptionSuccessAlert = false

    // Settings
    public var isShowingSettings = false
    public var unreadNotificationReminder = false
    public var homePageIdentityDisplay = true
    public var scanDisplay = true
    public var historyRecordKeep = true
    public var historyKeepDays = 50

    // Notification Settings
    public var notificationSettings: NotificationSettings.State?

    // Developer Mode
    public var isShowingDeveloperPanel = false

    public init(user: UserStateCore.User? = nil) {
      self.user = user
    }

    public var displayName: String {
      user?.displayName ?? "未知用户"
    }

    public var initials: String {
      let components = displayName.components(separatedBy: " ")
      let initials = components.compactMap { $0.first }.prefix(2)
      return String(initials).uppercased()
    }
  }
  
  public enum Action: BindableAction, Sendable {
    case binding(BindingAction<State>)
    case onAppear
    case loadProfile
    case profileLoaded(UserStateCore.User)
    case profileLoadFailed(ProfileError)
    case loadUserProfile
    case userProfileLoaded(UserProfileResponse)
    case userProfileLoadFailed(ProfileError)
    case editProfileButtonTapped
    case saveProfileButtonTapped
    case cancelEditingButtonTapped
    case logoutButtonTapped
    case deleteAccountButtonTapped
    case confirmDeleteAccount
    case cancelDeleteAccount
    case deleteAccountCompleted
    case dismissDeleteSuccessAlert
    case dismissSubscriptionSuccessAlert
    case subscriptionStatusUpdated(SubscriptionStatus)
    case refreshUserStatus
    case refreshCompleted
    case clearError
    case backToHome  // 新增：返回主页面
    case setUser(User)  // 设置用户数据

    // Settings actions
    case showSettings
    case hideSettings
    case toggleUnreadNotificationReminder
    case toggleHomePageIdentityDisplay
    case toggleScanDisplay
    case toggleHistoryRecordKeep
    case updateHistoryKeepDays(Int)

    // About actions
    case showDeveloperInfo
    case showRating
    case shareApp
    case showFeedback
    case showVersionInfo

    // Developer Mode actions
    case showDeveloperPanel
    case dismissDeveloperPanel
    case testAPIConnection
    
    // New settings actions for bridal app
    case upgradeSubscription
    case showNotificationSettings
    case dismissNotificationSettings
    case notificationSettings(NotificationSettings.Action)
    case showPrivacySettings
    case showPhotoQualitySettings
    case showCloudSyncSettings
    case showHelp
    
    // Payment related actions
    case subscriptionPurchaseStarted
    case subscriptionPurchaseCompleted(UserStateCore.SubscriptionStatus)
    case subscriptionPurchaseFailed(String)
    
    // Navigation to subscription page
    case showSubscriptionPage

    // Development only - Reset subscription status for testing
    #if DEBUG
    case resetSubscriptionForTesting
    #endif
  }
  
  @Dependency(\.authenticationClient) var authenticationClient
  @Dependency(\.userStorageClient) var userStorageClient
  @Dependency(\.storeKitClient) var storeKitClient
  @Dependency(\.networkClient) var networkClient
  
  public init() {}
  
  public var body: some Reducer<State, Action> {
    BindingReducer()

    Reduce { state, action in
      switch action {
      case .binding:
        return .none
        
      case .onAppear:
        // 如果已经有用户数据，加载详细的用户信息
        if let user = state.user {
          print("📱 个人中心页面出现，用户数据已存在: \(user.displayName)")
          return .send(.loadUserProfile)
        }

        // 对于第一次登录的用户，不尝试加载用户资料，避免显示错误
        // 用户数据应该在登录成功后通过其他方式设置到ProfileCore中
        print("📱 个人中心页面出现，等待用户数据设置（第一次登录或数据未同步）")
        return .none

      case .setUser(let user):
        print("✅ ProfileCore: 设置用户数据")
        print("   用户: \(user.displayName) (\(user.email))")
        print("   订阅状态: \(user.subscriptionStatus.displayName)")

        state.user = user
        state.isLoading = false
        state.error = nil

        return .none
        
      case .loadProfile:
        // 这个action现在主要用于刷新用户状态，而不是初始加载
        // 如果用户数据不存在，说明可能是登录状态异常
        guard state.user != nil else {
          print("⚠️ 尝试加载用户资料，但用户未登录或数据缺失")
          // 不设置错误状态，避免对第一次登录用户显示错误
          return .none
        }

        state.isLoading = true
        state.error = nil

        return .run { send in
          do {
            let user = try await loadUserProfile()
            await send(.profileLoaded(user))
          } catch {
            await send(.profileLoadFailed(.networkError(error.localizedDescription)))
          }
        }
        
      case let .profileLoaded(user):
        state.isLoading = false
        state.user = user
        return .none
        
      case let .profileLoadFailed(error):
        state.isLoading = false
        state.error = error
        return .none

      case .loadUserProfile:
        print("🔄 开始加载用户详细信息...")
        Task { @MainActor in
          LogManager.shared.info("开始加载用户详细信息", category: "Profile")
        }
        state.isLoadingProfile = true

        return .run { send in
          do {
            print("🌐 开始调用用户详细信息API...")
            await MainActor.run {
              LogManager.shared.info("开始调用用户详细信息API", category: "Profile")
            }

            // 获取存储的token
            guard let (_, token) = try await userStorageClient.loadUser() else {
              let errorMsg = "未找到认证token"
              await MainActor.run {
                LogManager.shared.error(errorMsg, category: "Profile")
              }
              throw ProfileError.networkError(errorMsg)
            }

            print("🔑 使用token: \(token.prefix(20))...")
            await MainActor.run {
              LogManager.shared.debug("使用token: \(token.prefix(20))...", category: "Profile")
            }

            // 构建请求
            guard let baseURL = URL(string: APIEndpoints.baseURL) else {
              throw ProfileError.networkError("无效的API地址")
            }

            let request = try RequestBuilder(baseURL: baseURL)
              .path("/api/v1/login/test-token")
              .method(.POST)
              .header("Authorization", "Bearer \(token)")
              .header("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
              .header("Accept", "*/*")
              .header("Host", baseURL.host! + ":" + String(baseURL.port ?? 8000))
              .header("Connection", "keep-alive")
              .build()

            print("🚀 发送请求到: \(request.url.absoluteString)")
            print("📋 请求头: \(request.headers)")
            await MainActor.run {
              LogManager.shared.logNetworkRequest(
                url: request.url.absoluteString,
                method: "POST",
                headers: request.headers
              )
            }

            // 发送请求
            let startTime = Date()
            let data = try await networkClient.request(request)
            let responseTime = Date().timeIntervalSince(startTime)

            print("✅ 收到响应，数据大小: \(data.count) bytes")
            await MainActor.run {
              LogManager.shared.logNetworkResponse(
                url: request.url.absoluteString,
                statusCode: 200,
                responseTime: responseTime
              )
            }

            // 解析响应
            let decoder = JSONDecoder()
            let userProfile = try decoder.decode(UserProfileResponse.self, from: data)

            print("🎉 用户详细信息解析成功:")
            print("   邮箱: \(userProfile.email)")
            print("   全名: \(userProfile.fullName)")
            print("   创建时间: \(userProfile.createdAt)")
            print("   最后登录: \(userProfile.lastLogin)")

            await MainActor.run {
              LogManager.shared.info("用户详细信息解析成功: \(userProfile.email)", category: "Profile")
            }
            await send(.userProfileLoaded(userProfile))
          } catch {
            print("❌ 加载用户详细信息失败: \(error)")
            await MainActor.run {
              LogManager.shared.error("加载用户详细信息失败: \(error.localizedDescription)", category: "Profile")
              LogManager.shared.logNetworkError(url: "/api/v1/login/test-token", error: error)
            }
            await send(.userProfileLoadFailed(.networkError(error.localizedDescription)))
          }
        }

      case let .userProfileLoaded(userProfile):
        print("✅ 用户详细信息加载成功")
        print("   邮箱: \(userProfile.email)")
        print("   全名: \(userProfile.fullName)")
        print("   创建时间: \(userProfile.createdAt)")
        print("   最后登录: \(userProfile.lastLogin)")
        print("   总积分: \(userProfile.totalCredits)")
        print("   剩余积分: \(userProfile.remainingCredits)")
        print("   已使用积分: \(userProfile.totalCredits - userProfile.remainingCredits)")

        state.isLoadingProfile = false
        state.userProfile = userProfile
        return .none

      case let .userProfileLoadFailed(error):
        print("❌ 用户详细信息加载失败: \(error)")
        state.isLoadingProfile = false
        // 不设置error，避免影响其他功能
        return .none
        
      case .editProfileButtonTapped:
        guard let user = state.user else { return .none }
        state.isEditingProfile = true
        state.editableUser = EditableUser(from: user)
        return .none
        
      case .saveProfileButtonTapped:
        guard let editableUser = state.editableUser else { return .none }
        
        state.isLoading = true
        
        return .run { [editableUser] send in
          do {
            let updatedUser = try await saveUserProfile(editableUser)
            await send(.profileLoaded(updatedUser))
          } catch {
            await send(.profileLoadFailed(.updateFailed(error.localizedDescription)))
          }
        }
        
      case .cancelEditingButtonTapped:
        state.isEditingProfile = false
        state.editableUser = nil
        return .none
        
      case .logoutButtonTapped:
        state.isLoading = true
        state.error = nil
        print("🚪 用户点击退出登录")

        return .run { send in
          do {
            // 清除Keychain中的用户数据和token
            try await userStorageClient.deleteUser()
            
            // 调用认证客户端的登出方法
            try await authenticationClient.logout()
            
            print("✅ 退出登录处理完成，Keychain数据已清除")
          } catch {
            print("❌ 退出登录过程中出现错误: \(error)")
            // 即使出错也继续退出流程，因为用户意图明确
          }
        }
        .cancellable(id: "logout", cancelInFlight: true)
        // This action will be handled by the parent reducer

      case .deleteAccountButtonTapped:
        state.isShowingDeleteAccountAlert = true
        return .none

      case .confirmDeleteAccount:
        state.isShowingDeleteAccountAlert = false
        state.isLoading = true
        state.error = nil

        return .run { send in
          do {
            // 模拟删除账号API调用
            print("🗑️ 开始删除账号...")
            try await Task.sleep(nanoseconds: 2_000_000_000) // 2秒延迟模拟API调用

            print("✅ 账号删除API调用成功")
            
            // 清除本地Keychain数据
            try await userStorageClient.deleteUser()
            print("✅ 本地用户数据已清除")
            
            await send(.deleteAccountCompleted)
          } catch {
            print("❌ 删除账号失败: \(error)")
            await send(.profileLoadFailed(.networkError("删除账号失败")))
          }
        }
        .cancellable(id: "deleteAccount", cancelInFlight: true)

      case .cancelDeleteAccount:
        state.isShowingDeleteAccountAlert = false
        return .none

      case .deleteAccountCompleted:
        state.isLoading = false
        state.isShowingDeleteSuccessAlert = true
        print("🎉 账号删除流程完成，显示成功提示")
        // 这个action会被父级reducer处理，执行实际的数据清除和退出登录
        return .none

      case .dismissDeleteSuccessAlert:
        state.isShowingDeleteSuccessAlert = false
        return .none

      case .dismissSubscriptionSuccessAlert:
        state.isShowingSubscriptionSuccessAlert = false
        return .none

      case .subscriptionStatusUpdated(let newStatus):
        print("📥 ProfileCore: 收到订阅状态更新请求: \(newStatus.displayName)")
        print("📊 ProfileCore: 当前用户状态: \(state.user?.subscriptionStatus.displayName ?? "nil")")

        // 更新用户的订阅状态
        if let currentUser = state.user {
          let updatedUser = User(
            id: currentUser.id,
            email: currentUser.email,
            displayName: currentUser.displayName,
            avatarURL: currentUser.avatarURL,
            createdAt: currentUser.createdAt,
            subscriptionStatus: newStatus
          )
          // 强制触发状态变化
          let oldStatus = state.user?.subscriptionStatus
          state.user = updatedUser
          state.isShowingSubscriptionSuccessAlert = true

          print("🎉 ProfileCore: 订阅状态已更新为: \(newStatus.displayName)")
          print("✅ ProfileCore: 用户对象已更新，新状态: \(updatedUser.subscriptionStatus.displayName)")
          print("🔄 ProfileCore: 状态变化 \(oldStatus?.displayName ?? "nil") -> \(updatedUser.subscriptionStatus.displayName)")

          // 保存订阅状态到Keychain
          return .run { [userStorageClient] send in
            do {
              @Dependency(\.keychainClient) var keychain
              
              // 保存订阅状态到Keychain
              let subscriptionData = try JSONEncoder().encode(newStatus)
              try await keychain.save(KeychainKeys.subscriptionStatus, subscriptionData)

              // 保存更新后的用户信息
              if let (_, token) = try await userStorageClient.loadUser() {
                try await userStorageClient.saveUser(updatedUser, token)
                print("✅ 订阅状态更新已持久化保存")
              }

              // 3秒后自动关闭成功提示
              try await Task.sleep(for: .seconds(3))
              await send(.dismissSubscriptionSuccessAlert)
            } catch {
              print("❌ 保存订阅状态失败: \(error)")
            }
          }
        }
        return .none

      case .refreshUserStatus:
        print("🔄 ProfileCore: 强制刷新用户状态（包括订阅和积分）")
        state.isLoading = true
        state.error = nil

        return .run { send in
          // 检查当前订阅状态
          let entitlements = await storeKitClient.currentEntitlements()

          if !entitlements.isEmpty {
            // 找到最新的有效订阅
            let latestTransaction = entitlements.first
            if let transaction = latestTransaction {
              // 根据产品ID确定订阅状态
              let expiryDate: Date
              switch transaction.productID {
              case "com.wenhaofree.bridal.sub_monthly_44":
                expiryDate = Calendar.current.date(byAdding: .month, value: 1, to: transaction.purchaseDate) ?? transaction.purchaseDate
              case "com.wenhaofree.bridal.sub_yearly_600":
                expiryDate = Calendar.current.date(byAdding: .year, value: 1, to: transaction.purchaseDate) ?? transaction.purchaseDate
              case "com.wenhaofree.bridal.lifetime_pro":
                expiryDate = Calendar.current.date(byAdding: .year, value: 100, to: transaction.purchaseDate) ?? transaction.purchaseDate
              default:
                expiryDate = transaction.expirationDate ?? Calendar.current.date(byAdding: .month, value: 1, to: transaction.purchaseDate) ?? transaction.purchaseDate
              }

              let currentStatus = UserStateCore.SubscriptionStatus.premium(
                expiryDate: expiryDate,
                usageLimits: UserStateCore.UsageLimits.monthlyPremium
              )
              print("📊 刷新订阅状态: \(currentStatus.displayName)")
              await send(.subscriptionStatusUpdated(currentStatus))
            } else {
              print("📊 刷新订阅状态: 无有效订阅")
              await send(.subscriptionStatusUpdated(.free))
            }
          } else {
            print("📊 刷新订阅状态: 无有效订阅")
            await send(.subscriptionStatusUpdated(.free))
          }

          // 完成刷新，停止加载状态
          await send(.refreshCompleted)
        }

      case .refreshCompleted:
        state.isLoading = false
        print("✅ ProfileCore: 用户状态刷新完成")
        return .none

      case .clearError:
        state.error = nil
        return .none

      // Settings actions
      case .showSettings:
        state.isShowingSettings = true
        return .none

      case .hideSettings:
        state.isShowingSettings = false
        return .none

      case .toggleUnreadNotificationReminder:
        state.unreadNotificationReminder.toggle()
        return .none

      case .toggleHomePageIdentityDisplay:
        state.homePageIdentityDisplay.toggle()
        return .none

      case .toggleScanDisplay:
        state.scanDisplay.toggle()
        return .none

      case .toggleHistoryRecordKeep:
        state.historyRecordKeep.toggle()
        return .none

      case let .updateHistoryKeepDays(days):
        state.historyKeepDays = days
        return .none

      // About actions
      case .showDeveloperInfo:
        // Handle showing developer info
        return .none

      case .showRating:
        // Handle showing app rating
        return .none

      case .shareApp:
        // Handle sharing app
        return .none

      case .showFeedback:
        // Handle showing feedback
        return .none

      case .showVersionInfo:
        // Handle showing version info
        return .none
        
      // New settings actions for bridal app
      case .upgradeSubscription:
        // 显示订阅页面让用户选择订阅方案
        print("💳 用户点击升级订阅，显示订阅选择页面")
        return .send(.showSubscriptionPage)
        
      case .subscriptionPurchaseStarted:
        state.isLoading = true
        print("💳 支付流程已开始")
        return .none
        
      case .subscriptionPurchaseCompleted(let newStatus):
        state.isLoading = false
        print("✅ 支付成功，订阅状态已更新")

        // 更新用户的订阅状态
        if let currentUser = state.user {
          // 创建新的用户对象，更新订阅状态
          let updatedUser = User(
            id: currentUser.id,
            email: currentUser.email,
            displayName: currentUser.displayName,
            avatarURL: currentUser.avatarURL,
            createdAt: currentUser.createdAt,
            subscriptionStatus: newStatus
          )
          state.user = updatedUser
          state.isShowingSubscriptionSuccessAlert = true
          print("📱 用户订阅状态已更新为: \(newStatus.displayName)")

          // 保存更新后的用户信息到存储
          return .run { [userStorageClient] send in
            do {
              @Dependency(\.keychainClient) var keychain
              
              // 保存订阅状态到Keychain
              let subscriptionData = try JSONEncoder().encode(newStatus)
              try await keychain.save(KeychainKeys.subscriptionStatus, subscriptionData)

              // 保存更新后的用户信息
              if let (_, token) = try await userStorageClient.loadUser() {
                try await userStorageClient.saveUser(updatedUser, token)
                print("✅ 用户信息和订阅状态已保存到Keychain")
              } else {
                print("⚠️ 无法获取当前token，仅保存订阅状态")
              }

              // 3秒后自动关闭成功提示
              try await Task.sleep(for: .seconds(3))
              await send(.dismissSubscriptionSuccessAlert)
            } catch {
              print("❌ 保存订阅状态失败: \(error)")
            }
          }
        }
        return .none
        
      case .subscriptionPurchaseFailed(let errorMessage):
        state.isLoading = false
        state.error = .networkError(errorMessage)
        print("❌ 支付失败: \(errorMessage)")
        return .none
        
      case .showNotificationSettings:
        print("📱 用户点击通知设置")
        state.notificationSettings = NotificationSettings.State()
        return .none

      case .dismissNotificationSettings:
        print("📱 关闭通知设置")
        state.notificationSettings = nil
        return .none

      case .notificationSettings:
        // Handle notification settings actions - handled by ifLet reducer
        return .none

      case .showDeveloperPanel:
        print("🛠️ 用户点击开发者模式")
        state.isShowingDeveloperPanel = true
        return .none

      case .dismissDeveloperPanel:
        print("🛠️ 关闭开发者模式")
        state.isShowingDeveloperPanel = false
        return .none

      case .testAPIConnection:
        print("🔗 测试API连接")

        return .run { send in
          await MainActor.run {
            LogManager.shared.info("开始测试API连接到: \(APIEndpoints.baseURL)", category: "Developer")
          }
          do {
            // 构建测试请求
            guard let baseURL = URL(string: APIEndpoints.baseURL) else {
              await MainActor.run {
                LogManager.shared.error("无效的API地址: \(APIEndpoints.baseURL)", category: "Developer")
              }
              return
            }

            let request = try RequestBuilder(baseURL: baseURL)
              .path("/health")  // 健康检查端点
              .method(.GET)
              .header("User-Agent", "BridalApp-iOS/1.0")
              .build()

            await MainActor.run {
              LogManager.shared.info("发送健康检查请求到: \(request.url.absoluteString)", category: "Developer")
            }

            let startTime = Date()
            let data = try await networkClient.request(request)
            let responseTime = Date().timeIntervalSince(startTime)

            await MainActor.run {
              LogManager.shared.info("API连接测试成功! 响应时间: \(String(format: "%.2f", responseTime))s, 数据大小: \(data.count) bytes", category: "Developer")
            }

          } catch {
            await MainActor.run {
              LogManager.shared.error("API连接测试失败: \(error.localizedDescription)", category: "Developer")
            }
          }
        }

      case .showPrivacySettings:
        // TODO: Navigate to privacy settings
        print("📱 用户点击隐私设置")
        return .none
        
      case .showPhotoQualitySettings:
        // TODO: Navigate to photo quality settings
        print("📱 用户点击照片质量设置")
        return .none
        
      case .showCloudSyncSettings:
        // TODO: Navigate to cloud sync settings
        print("📱 用户点击云同步设置")
        return .none
        
      case .showHelp:
        // TODO: Navigate to help center
        print("📱 用户点击帮助中心")
        return .none
        
      case .showSubscriptionPage:
        // Navigate to subscription page, handled by parent
        print("💳 用户点击订阅页面入口")
        return .none

      #if DEBUG
      case .resetSubscriptionForTesting:
        print("🧪 [DEBUG] 重置订阅状态用于测试")

        // 重置为免费状态
        let freeStatus = UserStateCore.SubscriptionStatus.free

        // 更新用户的订阅状态
        if let currentUser = state.user {
          let updatedUser = User(
            id: currentUser.id,
            email: currentUser.email,
            displayName: currentUser.displayName,
            avatarURL: currentUser.avatarURL,
            createdAt: currentUser.createdAt,
            subscriptionStatus: freeStatus
          )
          state.user = updatedUser

          print("🧪 [DEBUG] 订阅状态已重置为免费版")

          // 清除本地存储的订阅状态
          return .run { send in
            // 清除订阅状态
            UserPersistenceService.saveSubscriptionStatus(freeStatus)

            // 清除Keychain中的订阅状态
            await KeychainSubscriptionStorage.shared.clearSubscriptionStatus()

            // 保存更新后的用户信息
            if let (_, token) = UserPersistenceService.restoreUserSession() {
              UserPersistenceService.saveUserSession(user: updatedUser, token: token)
              print("🧪 [DEBUG] 订阅状态重置已持久化保存")
            }

            print("🧪 [DEBUG] 订阅状态重置完成")
          }
        }
        return .none
      #endif

      case .backToHome:
        // 返回主页面，由AppCore处理导航逻辑
        print("🏠 用户点击返回主页")
        return .none
      }
    }
    .ifLet(\.notificationSettings, action: \.notificationSettings) {
      NotificationSettings()
    }
  }
}

// MARK: - Models



public struct EditableUser: Equatable, Sendable {
  public var displayName: String

  public init(displayName: String) {
    self.displayName = displayName
  }

  public init(from user: UserStateCore.User) {
    self.displayName = user.displayName
  }
}

public enum ProfileError: Error, Equatable, LocalizedError, Sendable {
  case networkError(String)
  case updateFailed(String)
  case unauthorized
  case userNotFound
  case validationError(String)
  
  public var errorDescription: String? {
    switch self {
    case .networkError(let message):
      return "Network error: \(message)"
    case .updateFailed(let message):
      return "Failed to update profile: \(message)"
    case .unauthorized:
      return "You are not authorized to perform this action"
    case .userNotFound:
      return "User profile not found"
    case .validationError(let message):
      return "Validation error: \(message)"
    }
  }
}

// MARK: - API Response Models

private struct UserResponse: Codable {
  let id: String
  let email: String
  let firstName: String
  let lastName: String
  let avatarURL: String?
  let bio: String?
  let joinedAt: String
  let isVerified: Bool
}

private struct UpdateUserRequest: Codable {
  let firstName: String
  let lastName: String
  let bio: String
}

// MARK: - Private API Functions

private func loadUserProfile() async throws -> UserStateCore.User {
  // 这个函数现在主要用于刷新用户数据，而不是初始加载
  // 对于第一次登录的用户，应该通过其他方式设置用户数据
  print("⚠️ loadUserProfile被调用，这通常表示需要从服务器刷新用户数据")

  // 在实际应用中，这里应该从服务器获取用户数据
  // 现在我们抛出一个更友好的错误，但这个错误不应该显示给第一次登录的用户
  throw ProfileError.userNotFound
}

private func saveUserProfile(_ editableUser: EditableUser) async throws -> UserStateCore.User {
  // This is a mock implementation
  // In a real app, you would make an actual API call

  // Simulate network delay
  try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

  // Validate input
  guard !editableUser.displayName.trimmingCharacters(in: .whitespaces).isEmpty else {
    throw ProfileError.validationError("显示名称不能为空")
  }

  // Return updated user
  return UserStateCore.User(
    id: "user123",
    email: "<EMAIL>",
    displayName: editableUser.displayName,
    avatarURL: nil,
    createdAt: Calendar.current.date(byAdding: .month, value: -6, to: Date()) ?? Date(),
    subscriptionStatus: .free  // 初始状态为免费用户
  )
}

// MARK: - Real API Implementation (commented out)

/*
private func loadUserProfile() async throws -> User {
  let request = try RequestBuilder(baseURL: URL(string: "https://api.example.com")!)
    .path("/user/profile")
    .method(.GET)
    .acceptJSON()
    .bearerToken(getCurrentToken()) // You'd need to implement token management
    .build()
  
  let response: UserResponse = try await networkClient.requestDecodable(request, as: UserResponse.self)
  
  let dateFormatter = ISO8601DateFormatter()
  let joinedAt = dateFormatter.date(from: response.joinedAt) ?? Date()
  
  return User(
    id: response.id,
    email: response.email,
    firstName: response.firstName,
    lastName: response.lastName,
    avatarURL: response.avatarURL,
    bio: response.bio,
    joinedAt: joinedAt,
    isVerified: response.isVerified
  )
}

private func saveUserProfile(_ editableUser: EditableUser) async throws -> User {
  let requestBody = UpdateUserRequest(
    firstName: editableUser.firstName,
    lastName: editableUser.lastName,
    bio: editableUser.bio
  )
  
  let request = try RequestBuilder(baseURL: URL(string: "https://api.example.com")!)
    .path("/user/profile")
    .method(.PUT)
    .body(requestBody)
    .bearerToken(getCurrentToken())
    .build()
  
  let response: UserResponse = try await networkClient.requestDecodable(request, as: UserResponse.self)
  
  let dateFormatter = ISO8601DateFormatter()
  let joinedAt = dateFormatter.date(from: response.joinedAt) ?? Date()
  
  return User(
    id: response.id,
    email: response.email,
    firstName: response.firstName,
    lastName: response.lastName,
    avatarURL: response.avatarURL,
    bio: response.bio,
    joinedAt: joinedAt,
    isVerified: response.isVerified
  )
}
*/
