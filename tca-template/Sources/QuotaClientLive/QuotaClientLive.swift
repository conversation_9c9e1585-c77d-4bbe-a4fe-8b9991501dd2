import Foundation
import Composable<PERSON>rchitecture
import QuotaClient
import NetworkClient
import Keychain<PERSON>lient

#if canImport(UIKit)
import UIKit
#endif

// MARK: - Live Implementation Registration

extension QuotaClient {
  public static let liveValue = QuotaClient(
    getCurrentQuota: {
      return try await LiveQuotaClient.shared.getCurrentQuota()
    },
    refreshQuota: {
      return try await LiveQuotaClient.shared.refreshQuota()
    },
    canGenerate: {
      return try await LiveQuotaClient.shared.canGenerate()
    },
    consumeGeneration: {
      return try await LiveQuotaClient.shared.consumeGeneration()
    },
    getQuotaForUser: { userType in
      return try await LiveQuotaClient.shared.getQuotaForUser(userType)
    }
  )
}

// MARK: - Live Quota Client Implementation

@MainActor
public class LiveQuotaClient: Sendable {
  public static let shared = LiveQuotaClient()
  
  @Dependency(\.networkClient) var networkClient
  @Dependency(\.keychainClient) var keychainClient
  
  private var cachedQuota: QuotaInfo?
  private var lastFetchTime: Date?
  private let cacheExpiration: TimeInterval = 300 // 5 minutes
  
  private init() {}
  
  // MARK: - Public Methods
  
  public func getCurrentQuota() async throws -> QuotaInfo {
    // Return cached quota if still valid
    if let cached = cachedQuota,
       let lastFetch = lastFetchTime,
       Date().timeIntervalSince(lastFetch) < cacheExpiration {
      return cached
    }
    
    // Fetch fresh quota from server
    return try await refreshQuota()
  }
  
  public func refreshQuota() async throws -> QuotaInfo {
    let quotaInfo = try await fetchQuotaFromServer()
    
    // Update cache
    cachedQuota = quotaInfo
    lastFetchTime = Date()
    
    return quotaInfo
  }
  
  public func canGenerate() async throws -> Bool {
    let quota = try await getCurrentQuota() 
    return quota.canGenerate
  }
  
  public func consumeGeneration() async throws -> QuotaInfo {
    let request = ConsumeGenerationRequest()
    let networkRequest = try await buildNetworkRequest(for: request, path: "/api/quota/consume", method: .POST)
    
    let data = try await networkClient.request(networkRequest)
    let response = try JSONDecoder().decode(ConsumeGenerationResponse.self, from: data)
    
    let quotaInfo = QuotaInfo(
      userType: UserType(rawValue: response.userType) ?? .guest,
      remainingQuota: response.remainingQuota,
      totalQuota: response.totalQuota,
      quotaPeriod: QuotaPeriod(rawValue: response.quotaPeriod) ?? .monthly,
      refreshDate: response.refreshDate,
      deviceId: response.deviceId
    )
    
    // Update cache
    cachedQuota = quotaInfo
    lastFetchTime = Date()
    
    return quotaInfo
  }
  
  public func getQuotaForUser(_ userType: UserType) async throws -> QuotaInfo {
    let request = GetQuotaRequest(userType: userType.rawValue)
    let networkRequest = try await buildNetworkRequest(for: request, path: "/api/quota/status", method: .GET)
    
    let data = try await networkClient.request(networkRequest)
    let response = try JSONDecoder().decode(GetQuotaResponse.self, from: data)
    
    return QuotaInfo(
      userType: userType,
      remainingQuota: response.remainingQuota,
      totalQuota: response.totalQuota,
      quotaPeriod: QuotaPeriod(rawValue: response.quotaPeriod) ?? .monthly,
      refreshDate: response.refreshDate,
      deviceId: response.deviceId
    )
  }
  
  // MARK: - Private Methods
  
  private func fetchQuotaFromServer() async throws -> QuotaInfo {
    let request = GetQuotaRequest()
    let networkRequest = try await buildNetworkRequest(for: request, path: "/api/quota/status", method: .GET)
    
    let data = try await networkClient.request(networkRequest)
    let response = try JSONDecoder().decode(GetQuotaResponse.self, from: data)
    
    return QuotaInfo(
      userType: UserType(rawValue: response.userType) ?? .guest,
      remainingQuota: response.remainingQuota,
      totalQuota: response.totalQuota,
      quotaPeriod: QuotaPeriod(rawValue: response.quotaPeriod) ?? .monthly,
      refreshDate: response.refreshDate,
      deviceId: response.deviceId
    )
  }
  
  private func buildNetworkRequest<T: Codable>(for requestBody: T, path: String, method: HTTPMethod) async throws -> NetworkRequest {
    guard let baseURL = URL(string: "https://bridal-api.wenhaofree.com") else {
      throw NetworkError.invalidURL("https://bridal-api.wenhaofree.com")
    }
    
    var headers = ["Content-Type": "application/json"]
    
    // Add authentication token if available
    if let tokenData = try? await keychainClient.load("auth_token"),
       let token = String(data: tokenData, encoding: .utf8) {
      headers["Authorization"] = "Bearer \(token)"
    }
    
    // Add device fingerprint for security
    let deviceFingerprint = generateDeviceFingerprint()
    headers["X-Device-Fingerprint"] = deviceFingerprint
    
    let requestBuilder = RequestBuilder(baseURL: baseURL)
      .path(path)
      .method(method)
      .headers(headers)
    
    if method != .GET {
      return try requestBuilder.body(requestBody).build()
    } else {
      return try requestBuilder.build()
    }
  }
  
  private func getDeviceId() -> String {
    // Generate or retrieve device ID for guest users
    if let deviceId = UserDefaults.standard.string(forKey: "device_id") {
      return deviceId
    }
    
    let newDeviceId = UUID().uuidString
    UserDefaults.standard.set(newDeviceId, forKey: "device_id")
    return newDeviceId
  }
}

// MARK: - API Models

private struct GetQuotaRequest: Codable {
  let userType: String?
  let deviceId: String?
  
  init(userType: String? = nil) {
    self.userType = userType
    self.deviceId = UserDefaults.standard.string(forKey: "device_id")
  }
}

private struct GetQuotaResponse: Codable {
  let userType: String
  let remainingQuota: Int
  let totalQuota: Int
  let quotaPeriod: String
  let refreshDate: Date?
  let deviceId: String?
}

private struct ConsumeGenerationRequest: Codable {
  let deviceId: String?
  
  init() {
    self.deviceId = UserDefaults.standard.string(forKey: "device_id")
  }
}

private struct ConsumeGenerationResponse: Codable {
  let success: Bool
  let userType: String
  let remainingQuota: Int
  let totalQuota: Int
  let quotaPeriod: String
  let refreshDate: Date?
  let deviceId: String?
  let message: String?
}

// MARK: - Security Helpers

private func generateDeviceFingerprint() -> String {
  let deviceId = UserDefaults.standard.string(forKey: "device_id") ?? UUID().uuidString
  let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
  
  #if canImport(UIKit)
  let systemVersion = UIDevice.current.systemVersion
  #else
  let systemVersion = "Unknown"
  #endif
  
  let timestamp = Date().timeIntervalSince1970
  
  let components = [deviceId, appVersion, systemVersion, String(Int(timestamp / 3600))]
  let combined = components.joined(separator: "|")
  
  return combined.data(using: .utf8)?.base64EncodedString() ?? deviceId
}