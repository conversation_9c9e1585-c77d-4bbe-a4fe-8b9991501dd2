import Foundation
import ComposableArchitecture
import UserStateCore
import StoreKit
import LoggingClient
import NetworkClient
import AuthenticationClient
import CommonUI


// MARK: - Product Configuration

public enum ProductID: String, CaseIterable, Sendable {
  case monthlyPro = "com.wenhaofree.bridal.sub_monthly_44"
  case yearlyPro = "com.wenhaofree.bridal.sub_yearly_600"
  case singleBasic = "com.wenhaofree.bridal.single_basic"

  public static var allProductIDs: [String] {
    return allCases.map { $0.rawValue }
  }
}

// MARK: - StoreKit Error Types

@available(iOS 15.0, macOS 12.0, *)
public enum StoreKitClientError: Error, LocalizedError, Equatable, Sendable {
  case productNotFound
  case userCancelled
  case purchasePending
  case unknown
  case verificationFailed
  case networkError(String)
  case storeKitError(String)

  public var errorDescription: String? {
    switch self {
    case .productNotFound:
      return "Product not found"
    case .userCancelled:
      return "User cancelled the purchase"
    case .purchasePending:
      return "Purchase is pending approval"
    case .unknown:
      return "Unknown error occurred"
    case .verificationFailed:
      return "Transaction verification failed"
    case .networkError(let message):
      return "Network error: \(message)"
    case .storeKitError(let message):
      return "StoreKit error: \(message)"
    }
  }

  public static func == (lhs: StoreKitClientError, rhs: StoreKitClientError) -> Bool {
    switch (lhs, rhs) {
    case (.productNotFound, .productNotFound),
         (.userCancelled, .userCancelled),
         (.purchasePending, .purchasePending),
         (.unknown, .unknown),
         (.verificationFailed, .verificationFailed):
      return true
    case (.networkError(let lhsMessage), .networkError(let rhsMessage)):
      return lhsMessage == rhsMessage
    case (.storeKitError(let lhsMessage), .storeKitError(let rhsMessage)):
      return lhsMessage == rhsMessage
    default:
      return false
    }
  }
}

// MARK: - StoreKit Helper Functions

@available(iOS 15.0, macOS 12.0, *)
func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
  switch result {
  case .unverified:
    throw StoreKitClientError.verificationFailed
  case .verified(let safe):
    return safe
  }
}

// MARK: - Subscription Feature

@Reducer
public struct Subscription: Sendable {
  
  // MARK: - State
  
  @ObservableState
  public struct State: Equatable, Sendable {
    public var subscriptionPlans: [SubscriptionPlan] = []
    public var selectedPlan: SubscriptionPlan?
    public var isLoading = false
    public var error: SubscriptionError?
    public var isPurchasing = false
    public var currentSubscription: UserSubscription?
    public var selectedTemplate: String?
    public var showingSuccessAlert = false
    public var successMessage: String?

    public init(
      subscriptionPlans: [SubscriptionPlan] = [],
      selectedPlan: SubscriptionPlan? = nil,
      selectedTemplate: String? = nil
    ) {
      self.subscriptionPlans = subscriptionPlans
      self.selectedPlan = selectedPlan
      self.selectedTemplate = selectedTemplate
    }

    public var hasActiveSubscription: Bool {
      currentSubscription?.isActive ?? false
    }

    public var recommendedPlan: SubscriptionPlan? {
      subscriptionPlans.first { $0.isRecommended }
    }

    public var isPremiumActive: Bool {
      hasActiveSubscription
    }

    public var canAccessPremiumFeatures: Bool {
      isPremiumActive
    }
  }
  
  // MARK: - Action
  
  public enum Action: BindableAction, Sendable {
    case binding(BindingAction<State>)
    case onAppear
    case loadSubscriptionPlans
    case subscriptionPlansLoaded([SubscriptionPlan])
    case subscriptionPlansLoadFailed(SubscriptionError)
    case planSelected(SubscriptionPlan)
    case purchaseButtonTapped
    case purchaseCompleted(UserSubscription)
    case purchaseCancelled
    case purchaseFailed(SubscriptionError)
    case restorePurchasesButtonTapped
    case restoreCompleted
    case restoreFailed(SubscriptionError)
    case dismissButtonTapped
    case clearError
    case transactionUpdated(StoreKit.Transaction)
    case checkSubscriptionStatus
    case subscriptionStatusUpdated(UserSubscriptionStatus?)
    case proceedWithPremiumTemplate
    case dismissSuccessAlert
    case returnToPreviousFlow
    case addSinglePurchaseCredits(Int)
    case oneTimeRechargeCompleted(Int)
    case oneTimeRechargeFailed(SubscriptionError)
  }
  
  // MARK: - Dependencies
  
  @Dependency(\.storeKitClient) var storeKitClient
  @Dependency(\.subscriptionStorage) var subscriptionStorage
  @Dependency(\.loggingClient) var logger
  
  // MARK: - Initializer
  
  public init() {}

  // Public method to get current subscription status
  public static func getCurrentSubscriptionStatus() async -> UserSubscription? {
    @Dependency(\.storeKitClient) var storeKitClient

    // Check current entitlements
    let entitlements = await storeKitClient.currentEntitlements()

    for transaction in entitlements {
      let membershipTier = membershipTierFromProductID(transaction.productID)
      let subscriptionType = subscriptionTypeFromProductID(transaction.productID)

      let endDate: Date
      switch transaction.productID {
      case ProductID.monthlyPro.rawValue:
        endDate = Calendar.current.date(byAdding: .month, value: 1, to: transaction.purchaseDate) ?? transaction.purchaseDate
      case ProductID.yearlyPro.rawValue:
        endDate = Calendar.current.date(byAdding: .year, value: 1, to: transaction.purchaseDate) ?? transaction.purchaseDate
      default:
        endDate = transaction.expirationDate ?? Calendar.current.date(byAdding: .month, value: 1, to: transaction.purchaseDate) ?? transaction.purchaseDate
      }

      return UserSubscription(
        id: String(transaction.id),
        planId: transaction.productID,
        productID: transaction.productID,
        startDate: transaction.purchaseDate,
        endDate: endDate,
        isActive: !transaction.isUpgraded && (subscriptionType == .lifetime || Date() <= endDate),
        membershipTier: membershipTier,
        subscriptionType: subscriptionType
      )
    }

    return nil
  }
  
  // MARK: - Reducer Body
  
  public var body: some Reducer<State, Action> {
    BindingReducer()

    Reduce<State, Action> { state, action in
      switch action {
      case .binding:
        return .none

      case .onAppear:
        guard state.subscriptionPlans.isEmpty && !state.isLoading else {
          return .send(.checkSubscriptionStatus)
        }

        return .merge(
          .send(.loadSubscriptionPlans),
          .send(.checkSubscriptionStatus),
          .run { send in
            // Monitor transaction updates
            for await transaction in storeKitClient.transactionUpdates() {
              await send(.transactionUpdated(transaction))
            }
          }
        )

      case .loadSubscriptionPlans:
        state.isLoading = true
        state.error = nil

        return .run { send in
          do {
            let products = try await storeKitClient.loadProducts(ProductID.allProductIDs)
            let plans = createSubscriptionPlans(from: products)

            // If no products were loaded (e.g., in mock mode), use fallback plans
            if plans.isEmpty {
              print("⚠️ No products loaded, using fallback subscription plans")
              let fallbackPlans = createFallbackSubscriptionPlans()
              await send(.subscriptionPlansLoaded(fallbackPlans))
            } else {
              await send(.subscriptionPlansLoaded(plans))
            }
          } catch let error as StoreKitClientError {
            print("❌ StoreKit error, using fallback plans: \(error)")
            let fallbackPlans = createFallbackSubscriptionPlans()
            await send(.subscriptionPlansLoaded(fallbackPlans))
          } catch {
            print("❌ Network error, using fallback plans: \(error)")
            let fallbackPlans = createFallbackSubscriptionPlans()
            await send(.subscriptionPlansLoaded(fallbackPlans))
          }
        }

      case let .subscriptionPlansLoaded(plans):
        state.isLoading = false
        state.subscriptionPlans = plans
        state.selectedPlan = state.recommendedPlan ?? plans.first
        return .none

      case let .subscriptionPlansLoadFailed(error):
        state.isLoading = false
        state.error = error
        return .none

      case let .planSelected(plan):
        state.selectedPlan = plan
        return .none

      case .purchaseButtonTapped:
        print("🛒 Purchase button tapped!")
        print("📋 Current state:")
        print("   - Selected plan: \(state.selectedPlan?.name ?? "None")")
        print("   - Is purchasing: \(state.isPurchasing)")
        print("   - Plans count: \(state.subscriptionPlans.count)")

        guard let selectedPlan = state.selectedPlan else {
          print("❌ No plan selected")
          return .none
        }

        print("✅ Plan selected: \(selectedPlan.name) (\(selectedPlan.price))")
        print("🔍 Product available: \(selectedPlan.product != nil)")

        state.isPurchasing = true
        state.error = nil
        print("🔄 State updated: isPurchasing = true")

        // If we have a real product, use StoreKit
        if let product = selectedPlan.product {
          print("💳 Starting real StoreKit purchase for: \(selectedPlan.name)")
          return .run { [plan = selectedPlan] send in
            do {
              let result = try await storeKitClient.purchase(product)

              switch result {
              case .success(let transaction):
                let subscription = createUserSubscription(from: transaction, plan: plan)
                await send(.purchaseCompleted(subscription))

              case .userCancelled:
                print("🚫 用户取消了购买，返回订阅页面")
                await send(.purchaseCancelled)

              case .pending:
                await send(.purchaseFailed(.purchaseError("Purchase is pending approval")))
              }
            } catch let error as StoreKitClientError {
              await send(.purchaseFailed(.storeKitError(error)))
            } catch {
              await send(.purchaseFailed(.purchaseError(error.localizedDescription)))
            }
          }
        } else {
          // Fallback: No real product available (mock mode or product loading failed)
          print("⚠️ No real product available for: \(selectedPlan.name)")
          print("🔄 Attempting to reload products and retry purchase...")

          return .run { [plan = selectedPlan] send in
            do {
              // Try to reload products first
              let products = try await storeKitClient.loadProducts([plan.productID])

              if let product = products.first {
                print("✅ Product reloaded successfully, retrying purchase...")
                // Update the plan with the real product
                let updatedPlan = SubscriptionPlan(
                  id: plan.id,
                  productID: plan.productID,
                  name: plan.name,
                  description: plan.description,
                  price: product.displayPrice,
                  originalPrice: plan.originalPrice,
                  duration: plan.duration,
                  features: plan.features,
                  isRecommended: plan.isRecommended,
                  discountPercentage: plan.discountPercentage,
                  product: product
                )

                // Now try the purchase with the real product
                let result = try await storeKitClient.purchase(product)

                switch result {
                case .success(let transaction):
                  let subscription = createUserSubscription(from: transaction, plan: updatedPlan)
                  await send(.purchaseCompleted(subscription))

                case .userCancelled:
                  print("🚫 用户取消了购买，返回订阅页面")
                  await send(.purchaseCancelled)

                case .pending:
                  await send(.purchaseFailed(.purchaseError("Purchase is pending approval")))
                }
              } else {
                print("❌ Product still not available after reload")
                print("❌ 请检查StoreKit Configuration文件是否正确配置")
                print("❌ 产品ID: \(plan.productID)")
                await send(.purchaseFailed(.productNotFound))
              }
            } catch let error as StoreKitClientError {
              print("❌ StoreKit error in fallback: \(error)")
              await send(.purchaseFailed(.storeKitError(error)))
            } catch {
              print("❌ General error in fallback: \(error)")
              await send(.purchaseFailed(.purchaseError(error.localizedDescription)))
            }
          }
        }

      case let .purchaseCompleted(subscription):
        state.isPurchasing = false
        state.currentSubscription = subscription
        state.showingSuccessAlert = true

        // 检查是否是一次性支付，如果是则调用积分充值接口
        let isOneTimePurchase = subscription.productID == ProductID.singleBasic.rawValue

        if isOneTimePurchase {
          state.successMessage = "🎉 恭喜！您已成功购买单次生成\n正在为您充值积分..."

          return .run { send in
            // 调用一次性充值接口
            do {
              try await callOneTimeRechargeAPI()
              await send(.oneTimeRechargeCompleted(1)) // 单次购买增加1个积分
            } catch {
              print("❌ 一次性充值接口调用失败: \(error)")
              await send(.oneTimeRechargeFailed(.purchaseError("积分充值失败，请联系客服")))
            }
          }
        } else {
          state.successMessage = "🎉 恭喜！您已成功升级到Pro版本\n现在可以享受所有高级功能了！"

          return .run { send in
            // Save subscription status
            let newStatus = UserStateCore.SubscriptionStatus.premium(
              expiryDate: subscription.endDate,
              usageLimits: UserStateCore.UsageLimits.monthlyPremium
            )
            await subscriptionStorage.saveSubscriptionStatus(newStatus)

            // Auto dismiss after 2 seconds
            try await Task.sleep(for: .seconds(2))
            await send(.dismissSuccessAlert)
            await send(.proceedWithPremiumTemplate)
          }
        }

      case .purchaseCancelled:
        // 用户取消购买，重置购买状态但不显示错误
        state.isPurchasing = false
        // 清除任何现有错误
        state.error = nil
        print("✅ 用户取消购买，已重置状态，继续显示订阅页面")
        return .none

      case let .purchaseFailed(error):
        state.isPurchasing = false
        state.error = error
        return .none

      case .restorePurchasesButtonTapped:
        state.isLoading = true
        state.error = nil

        return .run { send in
          do {
            try await storeKitClient.restorePurchases()

            // Check current entitlements after restore
            let entitlements = await storeKitClient.currentEntitlements()

            if !entitlements.isEmpty {
              // Update subscription status based on entitlements
              await send(.checkSubscriptionStatus)
            }

            await send(.restoreCompleted)
          } catch let error as StoreKitClientError {
            await send(.restoreFailed(.storeKitError(error)))
          } catch {
            await send(.restoreFailed(.restoreError(error.localizedDescription)))
          }
        }

      case .restoreCompleted:
        state.isLoading = false
        return .none

      case let .restoreFailed(error):
        state.isLoading = false
        state.error = error
        return .none

      case .dismissButtonTapped:
        // This will be handled by the parent reducer
        return .none

      case .clearError:
        state.error = nil
        return .none

      case .transactionUpdated(_):
        // Handle transaction updates from StoreKit
        return .send(.checkSubscriptionStatus)

      case .checkSubscriptionStatus:
        return .run { send in
          // Check subscription status for all products
          var foundActiveSubscription = false

          for productID in ProductID.allCases {
            if let status = await storeKitClient.subscriptionStatus(productID.rawValue) {
              let subscriptionStatus = UserSubscriptionStatus(
                productID: productID.rawValue,
                isActive: status.state == .subscribed,
                expirationDate: nil, // Will be implemented when we have proper StoreKit integration
                renewalDate: nil
              )

              if status.state == .subscribed {
                foundActiveSubscription = true
              }

              await send(.subscriptionStatusUpdated(subscriptionStatus))
              break // Only need one active subscription
            }
          }

          if !foundActiveSubscription {
            await send(.subscriptionStatusUpdated(nil))
          }
        }

      case .subscriptionStatusUpdated(let status):
        if let status = status, status.isActive {
          // Update current subscription based on status
          let membershipTier = membershipTierFromProductID(status.productID)
          let subscriptionType = subscriptionTypeFromProductID(status.productID)

          let newSubscription = UserSubscription(
            id: UUID().uuidString,
            planId: status.productID,
            productID: status.productID,
            startDate: Date(),
            endDate: status.expirationDate ?? Date().addingTimeInterval(86400 * 30),
            isActive: status.isActive,
            membershipTier: membershipTier,
            subscriptionType: subscriptionType
          )

          state.currentSubscription = newSubscription
        } else {
          state.currentSubscription = nil
        }
        return .none

      case .proceedWithPremiumTemplate:
        // This will be handled by parent reducer (AppCore)
        return .none

      case .dismissSuccessAlert:
        state.showingSuccessAlert = false
        state.successMessage = nil
        return .none

      case .returnToPreviousFlow:
        // This will be handled by parent reducer (AppCore)
        return .none

      case let .addSinglePurchaseCredits(credits):
        // This action will be forwarded to the parent to update user state
        print("📝 Adding \(credits) single purchase credits")
        return .none

      case let .oneTimeRechargeCompleted(credits):
        state.showingSuccessAlert = true
        state.successMessage = "🎉 恭喜！积分充值成功\n您获得了 \(credits) 个生成积分！"

        return .run { send in
          // Auto dismiss after 2 seconds
          try await Task.sleep(for: .seconds(2))
          await send(.dismissSuccessAlert)
          await send(.addSinglePurchaseCredits(credits))
          // 一次性支付成功后也需要退出订阅页面
          await send(.returnToPreviousFlow)
        }

      case let .oneTimeRechargeFailed(error):
        state.isPurchasing = false
        state.error = error
        return .none
      }
    }
  }
}

// MARK: - Models

public enum MembershipTier: String, Equatable, Sendable, CaseIterable {
  case free = "free"
  case monthlyPro = "monthly_pro"
  case yearlyPro = "yearly_pro"

  public var displayName: String {
    switch self {
    case .free:
      return "Free"
    case .monthlyPro:
      return "Monthly Pro"
    case .yearlyPro:
      return "Yearly Pro"
    }
  }

  public var isPremium: Bool {
    switch self {
    case .free:
      return false
    case .monthlyPro, .yearlyPro:
      return true
    }
  }
}

public enum SubscriptionType: String, Equatable, Sendable, CaseIterable {
  case none = "none"
  case monthly = "monthly"
  case yearly = "yearly"
  case lifetime = "lifetime"

  public var displayName: String {
    switch self {
    case .none:
      return "No Subscription"
    case .monthly:
      return "Monthly"
    case .yearly:
      return "Yearly"
    case .lifetime:
      return "Lifetime"
    }
  }
}

public struct SubscriptionPlan: Equatable, Sendable, Identifiable {
  public let id: String
  public let productID: String
  public let name: String
  public let description: String
  public let price: String
  public let originalPrice: String?
  public let duration: String
  public let features: [String]
  public let isRecommended: Bool
  public let discountPercentage: Int?
  public let product: StoreKit.Product?

  public init(
    id: String,
    productID: String,
    name: String,
    description: String,
    price: String,
    originalPrice: String? = nil,
    duration: String,
    features: [String],
    isRecommended: Bool = false,
    discountPercentage: Int? = nil,
    product: StoreKit.Product? = nil
  ) {
    self.id = id
    self.productID = productID
    self.name = name
    self.description = description
    self.price = price
    self.originalPrice = originalPrice
    self.duration = duration
    self.features = features
    self.isRecommended = isRecommended
    self.discountPercentage = discountPercentage
    self.product = product
  }
}

public struct UserSubscription: Equatable, Sendable {
  public let id: String
  public let planId: String
  public let productID: String
  public let startDate: Date
  public let endDate: Date
  public let isActive: Bool
  public let membershipTier: MembershipTier
  public let subscriptionType: SubscriptionType

  public init(
    id: String,
    planId: String,
    productID: String,
    startDate: Date,
    endDate: Date,
    isActive: Bool,
    membershipTier: MembershipTier = .free,
    subscriptionType: SubscriptionType = .none
  ) {
    self.id = id
    self.planId = planId
    self.productID = productID
    self.startDate = startDate
    self.endDate = endDate
    self.isActive = isActive
    self.membershipTier = membershipTier
    self.subscriptionType = subscriptionType
  }

  public var isExpired: Bool {
    guard subscriptionType != .lifetime else { return false }
    return Date() > endDate
  }

  public var daysUntilExpiration: Int? {
    guard subscriptionType != .lifetime && isActive else { return nil }
    let calendar = Calendar.current
    return calendar.dateComponents([.day], from: Date(), to: endDate).day
  }

  public var formattedExpirationDate: String {
    switch subscriptionType {
    case .lifetime:
      return "Never expires"
    case .monthly, .yearly:
      let formatter = DateFormatter()
      formatter.dateStyle = .medium
      formatter.timeStyle = .none
      return formatter.string(from: endDate)
    case .none:
      return "No active subscription"
    }
  }

  public var membershipDisplayName: String {
    switch membershipTier {
    case .free:
      return "Free"
    case .monthlyPro:
      return "Monthly Pro"
    case .yearlyPro:
      return "Yearly Pro"
    }
  }
}

public struct UserSubscriptionStatus: Equatable, Sendable {
  public let productID: String
  public let isActive: Bool
  public let expirationDate: Date?
  public let renewalDate: Date?

  public init(
    productID: String,
    isActive: Bool,
    expirationDate: Date? = nil,
    renewalDate: Date? = nil
  ) {
    self.productID = productID
    self.isActive = isActive
    self.expirationDate = expirationDate
    self.renewalDate = renewalDate
  }
}

// Legacy SubscriptionProduct for backward compatibility
public struct SubscriptionProduct: Identifiable, Equatable, Sendable {
  public let id: String
  public let displayName: String
  public let description: String
  public let price: String
  public let priceValue: Decimal
  public let currencyCode: String
  public let productType: ProductType
  public let duration: SubscriptionDuration?  // nil for single purchases
  public let usageLimits: UserStateCore.UsageLimits
  public let isPopular: Bool

  public init(
    id: String,
    displayName: String,
    description: String,
    price: String,
    priceValue: Decimal,
    currencyCode: String,
    productType: ProductType,
    duration: SubscriptionDuration? = nil,
    usageLimits: UserStateCore.UsageLimits,
    isPopular: Bool = false
  ) {
    self.id = id
    self.displayName = displayName
    self.description = description
    self.price = price
    self.priceValue = priceValue
    self.currencyCode = currencyCode
    self.productType = productType
    self.duration = duration
    self.usageLimits = usageLimits
    self.isPopular = isPopular
  }

  // Convenience computed properties
  public var isSubscription: Bool {
    return productType == .subscription
  }

  public var isSinglePurchase: Bool {
    return productType == .singlePurchase
  }

  public var generationLimit: String {
    if let singlePurchaseGenerations = usageLimits.singlePurchaseGenerations {
      return "\(singlePurchaseGenerations)次生成"
    } else if let monthlyGenerations = usageLimits.generationsPerMonth {
      return "每月\(monthlyGenerations)次"
    } else if let yearlyGenerations = usageLimits.generationsPerYear {
      return "每年\(yearlyGenerations)次"
    } else {
      return "无限制"
    }
  }
}

// MARK: - Product Type

public enum ProductType: String, CaseIterable, Sendable, Codable {
  case subscription = "subscription"
  case singlePurchase = "single_purchase"

  public var displayName: String {
    switch self {
    case .subscription: return "订阅"
    case .singlePurchase: return "单次购买"
    }
  }
}

// MARK: - Subscription Duration

public enum SubscriptionDuration: String, CaseIterable, Sendable, Codable {
  case monthly = "monthly"
  case quarterly = "quarterly"
  case yearly = "yearly"

  public var displayName: String {
    switch self {
    case .monthly: return "月度订阅"
    case .quarterly: return "季度订阅"
    case .yearly: return "年度订阅"
    }
  }

  public var savingsMessage: String? {
    switch self {
    case .monthly: return nil
    case .quarterly: return "节省17%"
    case .yearly: return "节省42%"
    }
  }

  public var duration: TimeInterval {
    switch self {
    case .monthly: return 30 * 24 * 60 * 60 // 30 days
    case .quarterly: return 90 * 24 * 60 * 60 // 90 days
    case .yearly: return 365 * 24 * 60 * 60 // 365 days
    }
  }
}



public enum SubscriptionError: Error, Equatable, LocalizedError, Sendable {
  case networkError(String)
  case purchaseError(String)
  case restoreError(String)
  case userCancelled
  case planNotFound
  case alreadySubscribed
  case productNotFound
  case verificationFailed
  case storeKitError(StoreKitClientError)

  public var errorDescription: String? {
    switch self {
    case .networkError(let message):
      return "Network error: \(message)"
    case .purchaseError(let message):
      return "Purchase failed: \(message)"
    case .restoreError(let message):
      return "Restore failed: \(message)"
    case .userCancelled:
      return "Purchase was cancelled"
    case .planNotFound:
      return "Subscription plan not found"
    case .alreadySubscribed:
      return "You already have an active subscription"
    case .productNotFound:
      return "Product not found in the App Store"
    case .verificationFailed:
      return "Transaction verification failed"
    case .storeKitError(let error):
      return error.errorDescription
    }
  }

  public var recoverySuggestion: String? {
    switch self {
    case .storeKitError, .purchaseError, .restoreError:
      return "请稍后重试或联系客服"
    case .productNotFound, .planNotFound:
      return "请检查网络连接后重试"
    case .userCancelled:
      return nil
    case .networkError:
      return "请检查网络连接"
    case .alreadySubscribed:
      return "请检查您的订阅状态"
    case .verificationFailed:
      return "请重试或联系客服"
    }
  }
}

// MARK: - Dependencies

@DependencyClient
public struct StoreKitClient: Sendable {
  public var loadProducts: @Sendable ([String]) async throws -> [StoreKit.Product] = { _ in [] }
  public var purchase: @Sendable (StoreKit.Product) async throws -> PurchaseResult = { _ in .userCancelled }
  public var restorePurchases: @Sendable () async throws -> Void = { }
  public var currentEntitlements: @Sendable () async -> [StoreKit.Transaction] = { [] }
  public var transactionUpdates: @Sendable () -> AsyncStream<StoreKit.Transaction> = { AsyncStream { _ in } }
  public var subscriptionStatus: @Sendable (String) async -> StoreKit.Product.SubscriptionInfo.Status? = { _ in nil }
}

public enum PurchaseResult: Sendable {
  case success(StoreKit.Transaction)
  case userCancelled
  case pending
}

extension StoreKitClient: DependencyKey {
  public static var liveValue: StoreKitClient {
    print("🛒 SubscriptionCore: 使用 StoreKitClientLive 实现...")
    
    if #available(iOS 15.0, macOS 12.0, *) {
      print("🛒 SubscriptionCore: iOS 15+ 平台，使用 StoreKit 2 实现")
      return LiveStoreKitClient.live  // 使用真实实现
    } else {
      print("🛒 SubscriptionCore: iOS 14- 平台，使用模拟实现")
      return mockStoreKitClient()
    }

    // For now, use mock implementation to avoid compilation issues
    // TODO: Implement full StoreKit 2 integration
    return mockStoreKitClient()
  }

  public static let testValue = StoreKitClient(
    loadProducts: { _ in [] },
    purchase: { _ in .userCancelled },
    restorePurchases: { },
    currentEntitlements: { [] },
    transactionUpdates: { AsyncStream { _ in } },
    subscriptionStatus: { _ in nil }
  )
}

// MARK: - Helper Functions for Product Details

private func getProductType(for productId: String) -> ProductType {
  switch productId {
  case "com.wenhaofree.bridal.single_basic":
    return .singlePurchase
  default:
    return .subscription
  }
}

private func getDuration(for productId: String) -> SubscriptionDuration? {
  switch productId {
  case "com.wenhaofree.bridal.sub_monthly_44":
    return .monthly
  case "com.wenhaofree.bridal.sub_yearly_600":
    return .yearly
  default:
    return nil
  }
}

private func getUsageLimits(for productId: String) -> UserStateCore.UsageLimits {
  switch productId {
  case "com.wenhaofree.bridal.single_basic":
    return UserStateCore.UsageLimits.singleUse
  case "com.wenhaofree.bridal.sub_monthly_44":
    return UserStateCore.UsageLimits.monthlyPremium
  case "com.wenhaofree.bridal.sub_yearly_600":
    return UserStateCore.UsageLimits.yearlyStandard
  default:
    return UserStateCore.UsageLimits.monthlyPremium
  }
}

// MARK: - Helper Functions

private func mockStoreKitClient() -> StoreKitClient {
  return StoreKitClient(
    loadProducts: { productIDs in
      print("🛒 Mock: Loading products for IDs: \(productIDs)")
      return createMockProducts(for: productIDs)
    },
    purchase: { product in
      print("💳 Mock: This should not be called for mock products")
      print("⚠️ Mock: Purchase flow should be handled in the reducer for mock products")

      // This should not be reached for mock products since we handle them in the reducer
      // But if it is called, simulate a successful purchase
      try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second delay
      return .userCancelled // Return cancelled to avoid complex mock transaction creation
    },
    restorePurchases: {
      print("🔄 Mock: Simulating restore purchases")
      // Simulate restore delay
      try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second delay
      print("✅ Mock: Restore completed")
    },
    currentEntitlements: {
      print("📋 Mock: Returning empty entitlements for local testing")
      return []
    },
    transactionUpdates: {
      print("🔄 Mock: Creating mock transaction updates stream")
      return AsyncStream { continuation in
        // For local testing, we don't need real transaction updates
        continuation.finish()
      }
    },
    subscriptionStatus: { productID in
      print("📊 Mock: Checking subscription status for \(productID)")
      return createMockSubscriptionStatus(for: productID)
    }
  )
}

// MARK: - Mock Product Creation

private func createMockProducts(for productIDs: [String]) -> [StoreKit.Product] {
  print("🏭 Mock: For local testing, returning empty products array")
  print("📝 Mock: Requested product IDs: \(productIDs)")
  print("💡 Mock: The fallback flow will handle mock purchases")

  // For local testing, we return empty array to trigger the fallback flow
  // which will create mock subscriptions directly
  return []
}

// MARK: - Mock Helpers (Simplified)

// MARK: - Mock Helpers

private func createMockEntitlements() -> [StoreKit.Transaction] {
  print("📜 Mock: Creating mock entitlements")
  // Return empty for local testing
  return []
}

private func createMockSubscriptionStatus(for productID: String) -> StoreKit.Product.SubscriptionInfo.Status? {
  print("📊 Mock: Creating mock subscription status for \(productID)")
  // Return nil for local testing - we'll handle subscription status differently
  return nil
}

// MARK: - Mock UserSubscription Creation

private func createMockUserSubscription(from plan: SubscriptionPlan) -> UserSubscription {
  print("🎭 Mock: Creating UserSubscription for \(plan.name)")

  let startDate = Date()
  let endDate: Date
  let membershipTier = membershipTierFromProductID(plan.productID)
  let subscriptionType = subscriptionTypeFromProductID(plan.productID)

  // Calculate end date based on subscription type
  switch plan.productID {
  case ProductID.monthlyPro.rawValue:
    endDate = Calendar.current.date(byAdding: .month, value: 1, to: startDate) ?? startDate
  case ProductID.yearlyPro.rawValue:
    endDate = Calendar.current.date(byAdding: .year, value: 1, to: startDate) ?? startDate
  case ProductID.singleBasic.rawValue:
    endDate = startDate // Single purchase doesn't have an end date
  default:
    endDate = Calendar.current.date(byAdding: .month, value: 1, to: startDate) ?? startDate
  }

  let subscription = UserSubscription(
    id: UUID().uuidString,
    planId: plan.id,
    productID: plan.productID,
    startDate: startDate,
    endDate: endDate,
    isActive: true,
    membershipTier: membershipTier,
    subscriptionType: subscriptionType
  )

  print("✅ Mock: Created subscription - \(subscription.membershipDisplayName) until \(endDate)")
  return subscription
}

// MARK: - Fallback Subscription Plans

private func createFallbackSubscriptionPlans() -> [SubscriptionPlan] {
  print("🔄 Creating fallback subscription plans...")

  return [
    SubscriptionPlan(
      id: ProductID.singleBasic.rawValue,
      productID: ProductID.singleBasic.rawValue,
      name: "单次生成",
      description: "1次高清图片生成",
      price: "¥1",
      originalPrice: nil,
      duration: "one-time",
      features: [
        "1次高清图片生成",
        "体验所有AI风格",
        "高分辨率输出"
      ],
      isRecommended: false,
      discountPercentage: nil,
      product: nil
    ),
    SubscriptionPlan(
      id: ProductID.monthlyPro.rawValue,
      productID: ProductID.monthlyPro.rawValue,
      name: "Monthly Pro",
      description: "Perfect for getting started",
      price: "¥28",
      originalPrice: nil,
      duration: "per month",
      features: [
        "每月40次高清生成",
        "解锁所有AI风格",
        "优先生成队列",
        "高分辨率输出",
        "无广告体验"
      ],
      isRecommended: false,
      discountPercentage: nil,
      product: nil
    ),
    SubscriptionPlan(
      id: ProductID.yearlyPro.rawValue,
      productID: ProductID.yearlyPro.rawValue,
      name: "Yearly Pro",
      description: "Best value for committed users",
      price: "¥128",
      originalPrice: "¥336",
      duration: "per year",
      features: [
        "每年600次高清生成",
        "包含月度版所有功能",
        "节省33%费用",
        "独家年度会员特权",
        "优先体验新功能",
        "高级模板库"
      ],
      isRecommended: true,
      discountPercentage: 33,
      product: nil
    ),
  ]
}

@DependencyClient
public struct SubscriptionStorage: Sendable {
  public var getSubscriptionStatus: @Sendable () async -> UserStateCore.SubscriptionStatus = { .free }
  public var saveSubscriptionStatus: @Sendable (UserStateCore.SubscriptionStatus) async -> Void = { _ in }
  public var clearSubscriptionStatus: @Sendable () async -> Void = { }
}

// MARK: - Keychain Storage Implementation

import Security

public actor KeychainSubscriptionStorage {
  public static let shared = KeychainSubscriptionStorage()

  private let service = "com.wenhaofree.bridal.subscription"
  private let account = "subscription_status"

  private init() {}

  public func getSubscriptionStatus() async -> UserStateCore.SubscriptionStatus {
    guard let data = getKeychainData() else {
      print("🔐 No subscription data found in Keychain")
      return .free
    }

    do {
      let decoder = JSONDecoder()
      decoder.dateDecodingStrategy = .iso8601
      let status = try decoder.decode(UserStateCore.SubscriptionStatus.self, from: data)
      print("🔐 Retrieved subscription status from Keychain: \(status)")
      return status
    } catch {
      print("❌ Failed to decode subscription status from Keychain: \(error)")
      return .free
    }
  }

  public func saveSubscriptionStatus(_ status: UserStateCore.SubscriptionStatus) async {
    do {
      let encoder = JSONEncoder()
      encoder.dateEncodingStrategy = .iso8601
      let data = try encoder.encode(status)

      let success = saveKeychainData(data)
      if success {
        print("🔐 Saved subscription status to Keychain: \(status)")
      } else {
        print("❌ Failed to save subscription status to Keychain")
      }
    } catch {
      print("❌ Failed to encode subscription status for Keychain: \(error)")
    }
  }

  public func clearSubscriptionStatus() async {
    let success = deleteKeychainData()
    if success {
      print("🔐 Cleared subscription status from Keychain")
    } else {
      print("❌ Failed to clear subscription status from Keychain")
    }
  }

  // MARK: - Private Keychain Methods

  private func getKeychainData() -> Data? {
    let query: [String: Any] = [
      kSecClass as String: kSecClassGenericPassword,
      kSecAttrService as String: service,
      kSecAttrAccount as String: account,
      kSecReturnData as String: true,
      kSecMatchLimit as String: kSecMatchLimitOne
    ]

    var result: AnyObject?
    let status = SecItemCopyMatching(query as CFDictionary, &result)

    guard status == errSecSuccess else {
      if status != errSecItemNotFound {
        print("❌ Keychain read error: \(status)")
      }
      return nil
    }

    return result as? Data
  }

  private func saveKeychainData(_ data: Data) -> Bool {
    // First try to update existing item
    let updateQuery: [String: Any] = [
      kSecClass as String: kSecClassGenericPassword,
      kSecAttrService as String: service,
      kSecAttrAccount as String: account
    ]

    let updateAttributes: [String: Any] = [
      kSecValueData as String: data
    ]

    let updateStatus = SecItemUpdate(updateQuery as CFDictionary, updateAttributes as CFDictionary)

    if updateStatus == errSecSuccess {
      return true
    } else if updateStatus == errSecItemNotFound {
      // Item doesn't exist, create new one
      let addQuery: [String: Any] = [
        kSecClass as String: kSecClassGenericPassword,
        kSecAttrService as String: service,
        kSecAttrAccount as String: account,
        kSecValueData as String: data,
        kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
      ]

      let addStatus = SecItemAdd(addQuery as CFDictionary, nil)
      return addStatus == errSecSuccess
    } else {
      print("❌ Keychain update error: \(updateStatus)")
      return false
    }
  }

  private func deleteKeychainData() -> Bool {
    let query: [String: Any] = [
      kSecClass as String: kSecClassGenericPassword,
      kSecAttrService as String: service,
      kSecAttrAccount as String: account
    ]

    let status = SecItemDelete(query as CFDictionary)
    return status == errSecSuccess || status == errSecItemNotFound
  }
}

extension SubscriptionStorage: DependencyKey {
  public static let liveValue = SubscriptionStorage(
    getSubscriptionStatus: {
      return await KeychainSubscriptionStorage.shared.getSubscriptionStatus()
    },
    saveSubscriptionStatus: { status in
      await KeychainSubscriptionStorage.shared.saveSubscriptionStatus(status)
    },
    clearSubscriptionStatus: {
      await KeychainSubscriptionStorage.shared.clearSubscriptionStatus()
    }
  )
  
  public static let testValue = SubscriptionStorage(
    getSubscriptionStatus: { .free },
    saveSubscriptionStatus: { _ in },
    clearSubscriptionStatus: { }
  )
}

extension DependencyValues {
  public var storeKitClient: StoreKitClient {
    get { self[StoreKitClient.self] }
    set { self[StoreKitClient.self] = newValue }
  }
  
  public var subscriptionStorage: SubscriptionStorage {
    get { self[SubscriptionStorage.self] }
    set { self[SubscriptionStorage.self] = newValue }
  }
}


// MARK: - Logging Category Extension

public extension LoggingClient {
  enum SubscriptionCategory: String, CaseIterable {
    case subscription = "Subscription"
  }
}

extension LoggingClient.SubscriptionCategory {
  public var category: String {
    return self.rawValue
  }
}

// MARK: - StoreKit Helper Functions

private func createSubscriptionPlans(from products: [StoreKit.Product]) -> [SubscriptionPlan] {
  var plans: [SubscriptionPlan] = []

  for product in products {
    let plan = createSubscriptionPlan(from: product)
    plans.append(plan)
  }

  return plans.sorted { lhs, rhs in
    // Sort by price: single, monthly, yearly
    let order = [ProductID.singleBasic.rawValue, ProductID.monthlyPro.rawValue, ProductID.yearlyPro.rawValue]
    let lhsIndex = order.firstIndex(of: lhs.productID) ?? Int.max
    let rhsIndex = order.firstIndex(of: rhs.productID) ?? Int.max
    return lhsIndex < rhsIndex
  }
}

private func createSubscriptionPlan(from product: StoreKit.Product) -> SubscriptionPlan {
  let features = getFeatures(for: product.id)
  let isRecommended = product.id == ProductID.yearlyPro.rawValue
  let discountPercentage = product.id == ProductID.yearlyPro.rawValue ? 33 : nil
  let originalPrice = product.id == ProductID.yearlyPro.rawValue ? "¥336" : nil

  let duration: String
  let name: String
  let description: String

  switch product.id {
  case ProductID.monthlyPro.rawValue:
    duration = "per month"
    name = "Monthly Pro"
    description = "Perfect for getting started"
  case ProductID.yearlyPro.rawValue:
    duration = "per year"
    name = "Yearly Pro"
    description = "Best value for committed users"
  case ProductID.singleBasic.rawValue:
    duration = "one-time"
    name = "Single Generation"
    description = "1次高清图片生成"
  default:
    duration = "unknown"
    name = product.displayName
    description = product.description
  }

  return SubscriptionPlan(
    id: product.id,
    productID: product.id,
    name: name,
    description: description,
    price: product.displayPrice,
    originalPrice: originalPrice,
    duration: duration,
    features: features,
    isRecommended: isRecommended,
    discountPercentage: discountPercentage,
    product: product
  )
}

private func createUserSubscription(from transaction: StoreKit.Transaction, plan: SubscriptionPlan) -> UserSubscription {
  let startDate = transaction.purchaseDate
  let endDate: Date
  let membershipTier = membershipTierFromProductID(plan.productID)
  let subscriptionType = subscriptionTypeFromProductID(plan.productID)

  switch plan.productID {
  case ProductID.monthlyPro.rawValue:
    endDate = Calendar.current.date(byAdding: .month, value: 1, to: startDate) ?? startDate
  case ProductID.yearlyPro.rawValue:
    endDate = Calendar.current.date(byAdding: .year, value: 1, to: startDate) ?? startDate
  default:
    endDate = transaction.expirationDate ?? Calendar.current.date(byAdding: .month, value: 1, to: startDate) ?? startDate
  }

  return UserSubscription(
    id: String(transaction.id),
    planId: plan.id,
    productID: plan.productID,
    startDate: startDate,
    endDate: endDate,
    isActive: true,
    membershipTier: membershipTier,
    subscriptionType: subscriptionType
  )
}

private func membershipTierFromProductID(_ productID: String) -> MembershipTier {
  switch productID {
  case ProductID.monthlyPro.rawValue:
    return .monthlyPro
  case ProductID.yearlyPro.rawValue:
    return .yearlyPro
  default:
    return .free
  }
}

private func subscriptionTypeFromProductID(_ productID: String) -> SubscriptionType {
  switch productID {
  case ProductID.monthlyPro.rawValue:
    return .monthly
  case ProductID.yearlyPro.rawValue:
    return .yearly
  default:
    return .none
  }
}

private func getFeatures(for productID: String) -> [String] {
  switch productID {
  case ProductID.monthlyPro.rawValue:
    return [
      "每月40次高清生成",
      "解锁所有AI风格",
      "优先生成队列",
      "高分辨率输出",
      "无广告体验"
    ]
  case ProductID.yearlyPro.rawValue:
    return [
      "每年600次高清生成",
      "包含月度版所有功能",
      "节省33%费用",
      "独家年度会员特权",
      "优先体验新功能",
      "高级模板库"
    ]
  case ProductID.singleBasic.rawValue:
    return [
      "1次高清图片生成",
      "体验所有AI风格",
      "高分辨率输出"
    ]
  default:
    return ["Premium features"]
  }
}

// MARK: - Live StoreKit 2 Implementation

@available(iOS 15.0, macOS 12.0, *)
public struct LiveStoreKitClient {

  public static var live: StoreKitClient {
    var client = StoreKitClient()

    client.loadProducts = { productIDs in
      print("🛒 Loading products from App Store...")
      print("🛒 Requested product IDs: \(productIDs)")

      do {
        let storeProducts = try await Product.products(for: productIDs)
        print("✅ Loaded \(storeProducts.count) products from App Store")

        // 详细打印每个产品信息
        for product in storeProducts {
          print("📦 Product: \(product.id) - \(product.displayName) - \(product.displayPrice)")
          print("   Type: \(product.type)")
          if let subscription = product.subscription {
            print("   Subscription: \(subscription.subscriptionPeriod)")
          }
        }

        // 验证产品配置
        for productID in productIDs {
          if !storeProducts.contains(where: { $0.id == productID }) {
            print("⚠️ Product ID '\(productID)' not found in StoreKit Configuration")
            print("⚠️ 请检查 bridalswift.storekit 文件中是否包含此产品ID")
          }
        }

        if storeProducts.isEmpty {
          print("❌ 没有加载到任何产品！")
          print("❌ 请确保：")
          print("   1. StoreKit Configuration文件已添加到项目中")
          print("   2. 在Xcode中选择了正确的StoreKit Configuration文件")
          print("   3. 产品ID在配置文件中正确定义")
        }

        return storeProducts

      } catch {
        print("❌ Failed to load products: \(error)")
        print("❌ Error details: \(error.localizedDescription)")
        print("❌ 这可能是因为：")
        print("   1. StoreKit Configuration文件未正确配置")
        print("   2. 网络连接问题")
        print("   3. App Store Connect配置问题")

        // Return empty array on error
        return []
      }
    }

    client.purchase = { product in
      print("💳 Starting purchase for: \(product.displayName)")
      print("💳 Product ID: \(product.id)")

      do {
        print("✅ Product: \(product.displayName) - \(product.displayPrice)")
        print("💰 Price: \(product.price) \(product.priceFormatStyle.currencyCode)")

        // 执行真实购买 - 这会触发App Store的支付界面
        print("💳 Initiating real App Store purchase...")
        let result = try await product.purchase()
        print("💳 Purchase result received")

        switch result {
        case .success(let verification):
          do {
            let transaction = try checkVerified(verification)
            print("✅ Purchase successful: \(transaction.productID)")
            return .success(transaction)
          } catch {
            print("❌ Transaction verification failed: \(error)")
            throw StoreKitClientError.verificationFailed
          }
        case .userCancelled:
          print("🚫 User cancelled the purchase")
          return .userCancelled
        case .pending:
          print("⏳ Purchase is pending approval")
          return .pending
        @unknown default:
          print("❌ Unknown purchase result")
          throw StoreKitClientError.unknown
        }

      } catch let error as StoreKitClientError {
        print("❌ StoreKit error: \(error)")
        throw error
      } catch {
        print("❌ Purchase failed with error: \(error)")
        print("❌ Error type: \(type(of: error))")
        throw StoreKitClientError.storeKitError(error.localizedDescription)
      }
    }

    client.restorePurchases = {
      print("🔄 Restoring purchases...")

      do {
        // 同步最新的交易状态
        try await AppStore.sync()
        print("✅ Purchases restored successfully")
      } catch {
        print("❌ Failed to restore purchases: \(error)")
        throw StoreKitClientError.storeKitError(error.localizedDescription)
      }
    }

    client.currentEntitlements = {
      var transactions: [Transaction] = []
      for await verification in Transaction.currentEntitlements {
        do {
          let transaction = try checkVerified(verification)
          transactions.append(transaction)
        } catch {
          print("⚠️ Failed to verify transaction: \(error)")
        }
      }
      return transactions
    }

    client.transactionUpdates = {
      AsyncStream { continuation in
        Task {
          for await verification in Transaction.updates {
            do {
              let transaction = try checkVerified(verification)
              continuation.yield(transaction)
            } catch {
              print("⚠️ Failed to verify transaction update: \(error)")
            }
          }
        }
      }
    }

    client.subscriptionStatus = getSubscriptionStatus

    return client
  }
}

// MARK: - Helper Functions

@available(iOS 15.0, macOS 12.0, *)
@Sendable func getSubscriptionStatus(productID: String) async -> StoreKit.Product.SubscriptionInfo.Status? {
  do {
    guard let product = try await Product.products(for: [productID]).first else {
      return nil
    }
    return try await product.subscription?.status.first
  } catch {
    print("❌ Failed to get subscription status: \(error)")
    return nil
  }
}

// MARK: - StoreKit Debug Helper

@available(iOS 15.0, macOS 12.0, *)
public struct StoreKitDebugHelper {

    public static func debugStoreKitConfiguration() async {
        print("🔍 === StoreKit Configuration Debug ===")

        let productIDs = [
            "com.wenhaofree.bridal.single_basic",
            "com.wenhaofree.bridal.sub_monthly_44",
            "com.wenhaofree.bridal.sub_yearly_600"
        ]

        print("📋 Requested Product IDs:")
        for productID in productIDs {
            print("   - \(productID)")
        }

        do {
            print("\n🛒 Loading products from StoreKit...")
            let products = try await Product.products(for: productIDs)

            print("✅ Successfully loaded \(products.count) products")

            if products.isEmpty {
                print("❌ 没有找到任何产品！")
                print("❌ 可能的原因：")
                print("   1. StoreKit Configuration文件未正确添加到项目")
                print("   2. 在Xcode Scheme中未选择StoreKit Configuration文件")
                print("   3. 产品ID在配置文件中不匹配")
                print("\n🛠️ 解决步骤：")
                print("   1. 确保 bridalswift.storekit 文件在项目中")
                print("   2. 在Xcode中：Product → Scheme → Edit Scheme → Options")
                print("   3. 选择 StoreKit Configuration: bridalswift.storekit")
                print("   4. 重新运行应用")
            } else {
                print("\n📦 产品详情：")
                for product in products {
                    print("   🏷️ ID: \(product.id)")
                    print("   📝 Name: \(product.displayName)")
                    print("   💰 Price: \(product.displayPrice)")
                    print("   🏪 Type: \(product.type)")

                    if let subscription = product.subscription {
                        print("   📅 Period: \(subscription.subscriptionPeriod)")
                        print("   👥 Group: \(subscription.subscriptionGroupID)")
                    }
                    print("   ---")
                }
            }

            // 检查缺失的产品
            let foundProductIDs = Set(products.map { $0.id })
            let requestedProductIDs = Set(productIDs)
            let missingProductIDs = requestedProductIDs.subtracting(foundProductIDs)

            if !missingProductIDs.isEmpty {
                print("\n⚠️ 缺失的产品ID：")
                for missingID in missingProductIDs {
                    print("   - \(missingID)")
                }
                print("\n🔧 请检查 bridalswift.storekit 文件中是否包含这些产品ID")
            }

        } catch {
            print("❌ 加载产品失败: \(error)")
            print("❌ 错误详情: \(error.localizedDescription)")

            print("\n🛠️ 故障排除：")
            print("   1. 检查网络连接")
            print("   2. 确保StoreKit Configuration文件正确配置")
            print("   3. 重启Xcode和模拟器")
            print("   4. 清理项目构建缓存")
        }

        print("\n🔍 === Debug Complete ===")
    }
}

// MARK: - One-Time Recharge API

/// 调用一次性充值接口
private func callOneTimeRechargeAPI() async throws {
  @Dependency(\.networkClient) var networkClient

  print("💰 开始调用一次性充值接口...")

  // 获取认证token
  guard let authHeader = AccessTokenManager.getAuthorizationHeader() else {
    print("❌ 没有有效的认证token")
    throw SubscriptionError.purchaseError("认证失败，请重新登录")
  }

  print("✅ 找到认证token: \(authHeader.prefix(30))...")

  // 构建请求 - 使用动态API地址
  let baseURL = APIEndpoints.baseURL
  guard let url = URL(string: "\(baseURL)/api/v1/subscriptions/one-time-recharge") else {
    print("❌ 无效的API URL: \(baseURL)")
    throw SubscriptionError.purchaseError("API配置错误")
  }

  let request = NetworkRequest(
    url: url,
    method: .POST,
    headers: [
      "Authorization": authHeader,
      "User-Agent": "BridalApp/1.0.0",
      "Accept": "*/*",
      "Content-Type": "application/json"
    ],
    body: nil,
    timeout: 30.0
  )

  print("🌐 发送充值请求到: \(url.absoluteString)")
  print("🌐 请求头: \(request.headers)")

  do {
    let responseData = try await networkClient.request(request)

    // 尝试解析响应
    if let responseString = String(data: responseData, encoding: .utf8) {
      print("✅ 充值接口调用成功")
      print("📄 响应内容: \(responseString)")
    } else {
      print("✅ 充值接口调用成功，响应数据大小: \(responseData.count) bytes")
    }

  } catch let error as NetworkError {
    print("❌ 网络请求失败: \(error)")

    switch error {
    case .httpError(let statusCode, let data):
      print("   HTTP状态码: \(statusCode)")
      if let data = data, let errorMessage = String(data: data, encoding: .utf8) {
        print("   错误信息: \(errorMessage)")
      }
      throw SubscriptionError.purchaseError("充值失败，服务器返回错误: \(statusCode)")

    case .noInternetConnection:
      throw SubscriptionError.purchaseError("网络连接失败，请检查网络设置")

    case .timeout:
      throw SubscriptionError.purchaseError("请求超时，请重试")

    default:
      throw SubscriptionError.purchaseError("充值失败: \(error.localizedDescription)")
    }

  } catch {
    print("❌ 未知错误: \(error)")
    throw SubscriptionError.purchaseError("充值失败: \(error.localizedDescription)")
  }
}

