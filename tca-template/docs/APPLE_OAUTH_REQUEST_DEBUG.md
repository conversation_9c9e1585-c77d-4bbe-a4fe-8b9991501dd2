# 🔧 Apple OAuth API请求调试指南

## 🎯 **问题分析**

从你提供的日志可以看到：

### ✅ **正常工作的部分**
1. **Apple ID登录成功**：获取到了用户凭证
2. **Identity Token获取成功**：长度864字节
3. **用户信息正确解析**：姓名、邮箱都正确获取
4. **请求构建成功**：URL、Headers、Body都正确构建

### ❌ **问题所在**
1. **连接被拒绝**：`Connection refused [61: Connection refused]`
2. **后端服务器未运行**：`Could not connect to the server`
3. **使用Mock Token**：API调用失败后回退到mock token

---

## 🔍 **已添加的调试功能**

我已经在代码中添加了详细的请求体日志输出，现在你应该能看到：

### **期望的新日志输出**
```
🔧 AppleOAuthAPIClient: 开始处理 Apple ID 凭证
   用户ID: 000465.be0936b343684b30aba37a62b28b8b42.0329
   邮箱: <EMAIL>
✅ Identity token 获取成功，长度: 864
✅ 检测到用户信息:
   姓: 浩
   名: 文
   邮箱: <EMAIL>
   这是首次授权登录
✅ 检测到真实邮箱: <EMAIL>
🔧 构建网络请求...
   Base URL: http://127.0.0.1:8000
   Path: /api/v1/oauth/apple/login
   Full URL: http://127.0.0.1:8000/api/v1/oauth/apple/login
✅ 网络请求构建成功
📋 完整请求体:
{
  "identity_token": "eyJraWQiOiJBNkdVVFR...",
  "platform": "ios",
  "user_info": {
    "firstName": "浩",
    "lastName": "文",
    "email": "<EMAIL>"
  },
  "real_email": "<EMAIL>"
}
📊 请求体大小: 1010 bytes
🍎 开始调用 Apple OAuth API
🔍 开始执行网络请求...
```

---

## 🛠️ **修复内容**

### **修复1：URL配置优化**
```swift
// 修复前
public static let baseURL = "http://localhost:8000"

// 修复后
#if DEBUG
public static let baseURL = "http://127.0.0.1:8000"  // 使用127.0.0.1而不是localhost
#else
public static let baseURL = "https://bridal-api.wenhaofree.com"  // 生产环境
#endif
```

### **修复2：添加详细请求体日志**
```swift
// 打印完整的请求体用于调试
do {
  let requestBodyData = try JSONEncoder().encode(requestData)
  if let requestBodyString = String(data: requestBodyData, encoding: .utf8) {
    logger.info(.authentication, "📋 完整请求体:")
    logger.info(.authentication, requestBodyString)
  }
  logger.info(.authentication, "📊 请求体大小: \\(requestBodyData.count) bytes")
} catch {
  logger.warning(.authentication, "⚠️ 无法序列化请求体用于日志: \\(error)")
}
```

---

## 🧪 **测试步骤**

### **步骤1：重新测试Apple ID登录**
1. **重新构建并运行应用**
2. **进行Apple ID登录**
3. **观察控制台日志**，现在应该能看到：
   - 完整的请求体JSON
   - 请求体大小
   - 更详细的错误信息

### **步骤2：启动后端服务器（可选）**
如果你有后端服务器，可以启动它来测试真实的API调用：

```bash
# 在8000端口启动你的后端服务器
# 例如：
python manage.py runserver 127.0.0.1:8000
# 或者
npm start
# 或者其他启动命令
```

### **步骤3：验证请求格式**
从新的日志中，你应该能看到类似这样的请求体：

```json
{
  "identity_token": "eyJraWQiOiJBNkdVVFR...",
  "platform": "ios",
  "user_info": {
    "firstName": "浩",
    "lastName": "文", 
    "email": "<EMAIL>"
  },
  "real_email": "<EMAIL>"
}
```

---

## 📋 **请求体字段说明**

### **必需字段**
- `identity_token`: Apple提供的JWT token（用于后端验证）
- `platform`: 固定为"ios"

### **可选字段**
- `user_info`: 首次登录时提供的用户信息
  - `firstName`: 用户的名
  - `lastName`: 用户的姓
  - `email`: 用户邮箱（可能为空）
- `real_email`: 客户端获取的真实邮箱

### **字段映射**
```swift
private enum CodingKeys: String, CodingKey {
  case identityToken = "identity_token"
  case platform = "platform"
  case userInfo = "user_info"
  case realEmail = "real_email"
}
```

---

## 🔧 **后端API期望格式**

如果你需要调整后端API来匹配这个请求格式，后端应该期望接收：

```python
# Python/Django 示例
{
    "identity_token": str,  # 必需 - Apple JWT token
    "platform": str,        # 必需 - "ios"
    "user_info": {          # 可选 - 首次登录时提供
        "firstName": str,   # 可选
        "lastName": str,    # 可选
        "email": str        # 可选
    },
    "real_email": str       # 可选 - 客户端获取的真实邮箱
}
```

---

## 🆘 **下一步调试**

现在请重新测试Apple ID登录，并提供：

1. **完整的新日志输出**（包括请求体）
2. **请求体的JSON格式**
3. **请求体大小**
4. **任何新的错误信息**

这些信息将帮助我们：
- 验证请求格式是否正确
- 确认数据是否完整
- 定位是网络问题还是数据格式问题
- 为后端API提供准确的接口规范

---

## 💡 **临时解决方案**

即使后端服务器未运行，应用现在也会：
1. **尝试调用真实API**
2. **记录详细的请求信息**
3. **在失败时回退到mock token**
4. **继续正常的登录流程**

这确保了用户体验不受影响，同时为调试提供了完整的信息。

---

**🎯 目标：获取完整的请求体日志，验证数据格式，为后端API调试提供准确信息。**