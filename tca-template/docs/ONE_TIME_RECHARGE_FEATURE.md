# 一次性支付积分充值功能

## 🎯 功能概述

在订阅页面完成一次性支付后，自动调用后端接口为用户充值积分，实现单次生成功能的积分增加。

## 🔧 实现方案

### 1. 新增Action类型

在`SubscriptionCore.swift`中添加了两个新的Action：

```swift
case oneTimeRechargeCompleted(Int)    // 充值成功
case oneTimeRechargeFailed(SubscriptionError)  // 充值失败
```

### 2. 修改购买完成逻辑

在`purchaseCompleted`处理中添加了一次性支付检测：

```swift
case let .purchaseCompleted(subscription):
  // 检查是否是一次性支付
  let isOneTimePurchase = subscription.productID == ProductID.singleBasic.rawValue
  
  if isOneTimePurchase {
    // 调用积分充值接口
    return .run { send in
      do {
        try await callOneTimeRechargeAPI()
        await send(.oneTimeRechargeCompleted(1))
      } catch {
        await send(.oneTimeRechargeFailed(.purchaseError("积分充值失败，请联系客服")))
      }
    }
  }
```

### 3. 网络请求实现

实现了`callOneTimeRechargeAPI()`函数：

```swift
private func callOneTimeRechargeAPI() async throws {
  @Dependency(\.networkClient) var networkClient
  
  // 获取认证token
  guard let authHeader = AccessTokenManager.getAuthorizationHeader() else {
    throw SubscriptionError.purchaseError("认证失败，请重新登录")
  }
  
  // 构建请求
  let request = NetworkRequest(
    url: URL(string: "http://127.0.0.1:8000/api/v1/subscriptions/one-time-recharge")!,
    method: .POST,
    headers: [
      "Authorization": authHeader,
      "User-Agent": "BridalApp/1.0.0",
      "Accept": "*/*",
      "Content-Type": "application/json"
    ]
  )
  
  // 发送请求
  let _ = try await networkClient.request(request)
}
```

## 📋 API接口规范

### 请求信息
- **URL**: `http://127.0.0.1:8000/api/v1/subscriptions/one-time-recharge`
- **方法**: `POST`
- **认证**: Bearer Token (从AccessTokenManager获取)

### 请求头
```
Authorization: bearer {token}
User-Agent: BridalApp/1.0.0
Accept: */*
Content-Type: application/json
```

### 响应处理
- **成功**: 任何2xx状态码都视为成功
- **失败**: 非2xx状态码会抛出相应错误

## 🔄 用户体验流程

### 一次性支付流程
1. 用户选择"单次生成"计划
2. 完成StoreKit支付
3. 系统检测到一次性支付
4. 显示"正在为您充值积分..."
5. 调用后端充值接口
6. 成功后显示"积分充值成功，您获得了1个生成积分！"
7. 2秒后自动关闭弹窗

### 订阅支付流程
1. 用户选择订阅计划
2. 完成StoreKit支付
3. 系统检测到订阅支付
4. 显示"恭喜！您已成功升级到Pro版本"
5. 保存订阅状态
6. 2秒后自动关闭弹窗

## 🛡️ 错误处理

### 网络错误
- **无网络连接**: "网络连接失败，请检查网络设置"
- **请求超时**: "请求超时，请重试"
- **HTTP错误**: "充值失败，服务器返回错误: {状态码}"

### 认证错误
- **无Token**: "认证失败，请重新登录"
- **Token过期**: 根据HTTP 401状态码处理

### 通用错误
- **未知错误**: "充值失败: {错误描述}"

## 🔍 调试信息

系统会输出详细的调试日志：

```
💰 开始调用一次性充值接口...
✅ 找到认证token: bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
🌐 发送充值请求到: http://127.0.0.1:8000/api/v1/subscriptions/one-time-recharge
🌐 请求头: ["Authorization": "bearer ...", ...]
✅ 充值接口调用成功
📄 响应内容: {...}
```

## 📱 用户界面更新

### 成功状态
- 弹窗标题: "🎉 恭喜！积分充值成功"
- 弹窗内容: "您获得了 1 个生成积分！"
- 按钮文字: "太棒了！"

### 失败状态
- 显示具体错误信息
- 用户可以重试或联系客服

## 🧪 测试建议

### 单元测试
1. 测试一次性支付检测逻辑
2. 测试网络请求构建
3. 测试错误处理逻辑

### 集成测试
1. 模拟成功的充值接口调用
2. 模拟各种网络错误情况
3. 测试认证token获取和使用

### 用户测试
1. 完整的一次性支付流程
2. 网络异常情况下的用户体验
3. 不同设备上的表现一致性

## 🔮 后续优化

1. **重试机制**: 网络失败时自动重试
2. **离线支持**: 支付成功但网络失败时的离线处理
3. **积分同步**: 与用户状态管理的深度集成
4. **分析统计**: 充值成功率和失败原因分析

## 📝 相关文件

- `Sources/SubscriptionCore/SubscriptionCore.swift` - 核心逻辑实现
- `Sources/SubscriptionSwiftUI/SubscriptionView.swift` - UI界面
- `Sources/AuthenticationClient/AuthenticationClient.swift` - 认证token管理
- `Sources/NetworkClient/NetworkClient.swift` - 网络请求客户端
