# 🔐 Access Token 管理和请求头自动添加功能

## 📋 问题分析

根据提供的Apple登录响应数据，发现了以下关键问题：

### 登录响应数据
```json
{
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ3MDUzNDAsInN1YiI6IjljNTA5OTM4LWU5OTYtNDU1MS04MGQyLWMwNzRjYjQxNDhkNCJ9.gNOIbrBxMtS77OKinjQi_FSmzDxTRfuNB9dh-Z5Meh8",
    "token_type": "bearer",
    "user": {
        "email": "<EMAIL>",
        "id": "9c509938-e996-4551-80d2-c074cb4148d4",
        "full_name": "test haha"
    }
}
```

### 发现的问题
1. **Token存储不一致**: Apple登录后token存储在UserDefaults，但其他Client从Keychain读取
2. **缺少统一认证**: 各个Client都有自己的token获取逻辑
3. **手动添加认证头**: 需要在每个请求中手动添加Authorization头

## 🔧 修复方案

### 1. 统一Token存储

**修改前**: 只存储到UserDefaults
```swift
UserDefaults.standard.set(response.accessToken, forKey: "apple_access_token")
UserDefaults.standard.set(response.tokenType, forKey: "apple_token_type")
```

**修改后**: 同时存储到UserDefaults和Keychain
```swift
// 存储到UserDefaults
UserDefaults.standard.set(response.accessToken, forKey: "apple_access_token")
UserDefaults.standard.set(response.tokenType, forKey: "apple_token_type")

// 同时存储到Keychain，供其他Client使用
let keychainQuery: [String: Any] = [
  kSecClass as String: kSecClassGenericPassword,
  kSecAttrAccount as String: "auth_token",
  kSecValueData as String: response.accessToken.data(using: .utf8)!
]

SecItemDelete(keychainQuery as CFDictionary)
let status = SecItemAdd(keychainQuery as CFDictionary, nil)
```

### 2. 增强AccessTokenManager

**新增功能**:
- 从Keychain获取token的方法
- 统一的认证头生成
- 多重备用机制

```swift
public struct AccessTokenManager {
  /// 获取Apple Access Token（优先）
  public static func getAppleAccessToken() -> String?
  
  /// 从Keychain获取Access Token（备用）
  public static func getKeychainAccessToken() -> String?
  
  /// 获取完整的Authorization Header
  public static func getAuthorizationHeader() -> String?
  
  /// 检查是否有有效Token
  public static func hasValidToken() -> Bool
  
  /// 清除所有Token
  public static func clearTokens()
}
```

### 3. 自动认证头扩展

**新增RequestBuilder扩展**:
```swift
public extension RequestBuilder {
  /// 自动添加认证头
  func autoAuth() -> Self {
    if let authHeader = AccessTokenManager.getAuthorizationHeader() {
      return header("Authorization", authHeader)
    }
    return self
  }
  
  /// 添加Apple认证头
  func appleAuth() -> Self
  
  /// 添加Keychain认证头  
  func keychainAuth() -> Self
}
```

### 4. 认证助手工具

**新增AuthHelper**:
```swift
public struct AuthHelper {
  /// 检查认证状态
  public static func hasValidAuth() -> Bool
  
  /// 获取当前认证头
  public static func getCurrentAuthHeader() -> String?
  
  /// 打印认证状态（调试用）
  public static func printAuthStatus()
}
```

## ✅ 验证结果

### 测试流程验证
```
🧪 测试Access Token存储和使用流程
==================================================
1️⃣ 模拟Apple登录成功，解析响应...
✅ 登录响应解析成功:
   Access Token: eyJhbGciOiJIUzI1NiIsInR5cCI6Ik...
   Token Type: bearer
   用户ID: 9c509938-e996-4551-80d2-c074cb4148d4
   用户邮箱: <EMAIL>
   用户姓名: test haha

2️⃣ 存储Access Token...
🔐 Token存储状态:
   UserDefaults: ✅
   Keychain: ✅

3️⃣ 验证Token存储...
🔍 Access Token 状态检查:
   ✅ Apple Token (UserDefaults): eyJhbGciOiJIUzI1NiIsInR5cCI6Ik...
   ✅ Keychain Token: eyJhbGciOiJIUzI1NiIsInR5cCI6Ik...
   ✅ Authorization Header: bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXV...

4️⃣ 模拟后续API调用...
🌐 模拟API请求: https://bridal-api.wenhaofree.com/api/v1/user/profile
✅ 请求头构建成功:
   Authorization: bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   Content-Type: application/json
   Accept: application/json
```

## 🎯 使用方式

### 1. 自动认证的API请求
```swift
// 使用autoAuth()自动添加认证头
let request = try RequestBuilder(baseURL: baseURL)
  .path("/api/v1/user/profile")
  .method(.GET)
  .contentTypeJSON()
  .acceptJSON()
  .autoAuth()  // 🔑 自动添加认证头
  .build()

let response = try await networkClient.request(request)
```

### 2. 检查认证状态
```swift
// 检查是否有有效认证
if AuthHelper.hasValidAuth() {
  print("✅ 用户已认证")
} else {
  print("❌ 用户未认证，需要登录")
}

// 打印详细认证状态
AuthHelper.printAuthStatus()
```

### 3. 使用认证客户端
```swift
let apiClient = AuthenticatedAPIClient(
  networkClient: networkClient,
  baseURL: URL(string: "https://bridal-api.wenhaofree.com")!
)

// 自动处理认证的API调用
let profileData = try await apiClient.getUserProfile()
let imageData = try await apiClient.generateImage(prompt: "romantic wedding")
let quotaData = try await apiClient.getQuotaStatus()
```

## 🔄 Token生命周期管理

### 存储策略
1. **主存储**: UserDefaults（快速访问）
2. **备用存储**: Keychain（安全存储，供其他Client使用）
3. **自动同步**: 登录时同时更新两个存储位置

### 获取策略
1. **优先级**: Apple Token (UserDefaults) > Keychain Token
2. **备用机制**: 如果UserDefaults中没有，自动从Keychain获取
3. **格式统一**: 自动添加正确的token_type前缀

### 清理策略
```swift
// 登出时清理所有token
AccessTokenManager.clearTokens()
```

## 📁 修改的文件

1. **`Sources/AuthenticationClient/AuthenticationClient.swift`**
   - 增强AccessTokenManager功能
   - 添加Keychain存储逻辑
   - 统一token获取机制

2. **`Sources/AuthenticationClient/AuthenticationHelper.swift`**
   - 新增认证助手工具
   - RequestBuilder认证扩展
   - 示例API客户端

3. **`test_access_token_flow.swift`**
   - 完整的测试验证脚本
   - 模拟真实的登录和API调用流程

## 🚀 功能特性

### 1. 自动认证
- 所有API请求只需添加`.autoAuth()`即可自动获取认证头
- 支持多种token来源的自动切换
- 无需手动管理token

### 2. 统一存储
- UserDefaults和Keychain双重存储
- 确保不同Client都能访问到token
- 提高系统可靠性

### 3. 调试友好
- 详细的认证状态打印
- 清晰的错误信息
- 完整的日志记录

### 4. 向后兼容
- 保持原有的token获取方式
- 新增功能不影响现有代码
- 渐进式升级

## 🎉 总结

现在Apple ID登录后的access_token能够：

1. **正确存储**: 同时存储到UserDefaults和Keychain
2. **自动使用**: 后续API请求自动添加认证头
3. **统一管理**: 通过AccessTokenManager统一管理
4. **调试便利**: 提供完整的调试和状态检查工具

**实际效果**: 
- 用户登录后，所有需要认证的API请求都会自动包含正确的Authorization头
- 格式：`Authorization: bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- 无需手动处理token，提高开发效率和代码可靠性

🎉 **Access Token管理功能完全实现！**
