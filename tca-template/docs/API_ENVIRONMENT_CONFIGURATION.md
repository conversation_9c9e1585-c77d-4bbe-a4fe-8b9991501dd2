# API环境配置说明

## 🎯 问题描述

TestFlight版本仍在使用开发环境IP（127.0.0.1:8000），而不是生产环境IP（https://bridal-api.wenhaofree.com）。

## 🔍 原因分析

### 原始代码逻辑
```swift
#if DEBUG  // 调试模式
    #if targetEnvironment(simulator)  // 模拟器
        "http://127.0.0.1:8000"  // 本地开发服务器
    #else  // 真机调试
        "http://***********:8000"  // 局域网开发服务器
    #endif
#else  // 发布模式（Release）
    "https://bridal-api.wenhaofree.com"  // 生产环境
#endif
```

### 问题根因
1. **TestFlight构建配置**：可能使用了Debug配置而非Release配置
2. **环境判断不准确**：简单的DEBUG/Release判断无法区分TestFlight和本地调试
3. **Archive设置错误**：Archive scheme可能设置为Debug模式

## 🔧 解决方案

### 方案1: 修复后的智能环境判断（已实现）

```swift
// 更精确的环境判断逻辑
let url: String
let environment: String

#if DEBUG
  #if targetEnvironment(simulator)
    // 模拟器开发环境
    url = "http://127.0.0.1:8000"
    environment = "模拟器开发环境"
  #else
    // 真机开发环境 - 检查是否为TestFlight
    if isTestFlightBuild() {
      url = "https://bridal-api.wenhaofree.com"  // TestFlight使用生产环境
      environment = "TestFlight生产环境"
    } else {
      url = "http://***********:8000"  // 本地真机调试
      environment = "真机开发环境"
    }
  #endif
#else
  // Release构建 - 生产环境
  url = "https://bridal-api.wenhaofree.com"
  environment = "Release生产环境"
#endif
```

### TestFlight检测函数
```swift
/// 检测当前是否为TestFlight构建
private func isTestFlightBuild() -> Bool {
  #if targetEnvironment(simulator)
  return false  // 模拟器永远不是TestFlight
  #else
  // 检查是否存在TestFlight特有的receipt文件
  guard let receiptURL = Bundle.main.appStoreReceiptURL else {
    return false
  }
  
  // TestFlight构建的receipt路径包含"sandboxReceipt"
  let receiptPath = receiptURL.path
  let isTestFlight = receiptPath.contains("sandboxReceipt")
  
  return isTestFlight
  #endif
}
```

## 📋 环境判断逻辑

### 环境类型识别
| 环境 | 条件 | API地址 | 说明 |
|------|------|---------|------|
| 模拟器开发 | `DEBUG` + `simulator` | `http://127.0.0.1:8000` | 本地开发服务器 |
| 真机开发 | `DEBUG` + `device` + `!TestFlight` | `http://***********:8000` | 局域网开发服务器 |
| TestFlight | `DEBUG` + `device` + `TestFlight` | `https://bridal-api.wenhaofree.com` | 生产环境 |
| App Store | `RELEASE` | `https://bridal-api.wenhaofree.com` | 生产环境 |

### 检测方法
1. **模拟器检测**：`targetEnvironment(simulator)`
2. **TestFlight检测**：检查receipt路径是否包含"sandboxReceipt"
3. **Release检测**：编译条件`#if DEBUG`的反向

## 🛠️ Xcode配置检查

### 1. 检查Archive配置
```
Product → Scheme → Edit Scheme...
→ Archive标签
→ Build Configuration: 确保设置为"Release"
```

### 2. 检查构建设置
```
Project Settings → Build Settings
→ Swift Compiler - Custom Flags
→ Active Compilation Conditions
→ Release配置应该没有DEBUG标志
```

### 3. 检查Scheme配置
```
Product → Scheme → Edit Scheme...
→ Run标签 → Build Configuration: Debug（开发时）
→ Archive标签 → Build Configuration: Release（发布时）
```

## 🔍 调试信息

### 日志输出
系统会输出详细的环境检测日志：
```
🔧 [APIEndpoints] 使用TestFlight生产环境配置: https://bridal-api.wenhaofree.com
🔍 [TestFlight检测] Receipt路径: /var/containers/Bundle/Application/.../StoreKit/sandboxReceipt
🔍 [TestFlight检测] 是否为TestFlight: true
💰 [MainTab] 一次性支付成功，增加 1 个积分
🌐 发送充值请求到: https://bridal-api.wenhaofree.com/api/v1/subscriptions/one-time-recharge
```

### 验证方法
1. **查看控制台日志**：确认使用的API地址
2. **网络请求监控**：使用Charles或Proxyman查看实际请求
3. **TestFlight测试**：在TestFlight版本中测试一次性支付功能

## 📱 不同环境的行为

### 开发环境（Xcode直接运行）
- **模拟器**：使用本地服务器 `http://127.0.0.1:8000`
- **真机调试**：使用局域网服务器 `http://***********:8000`

### TestFlight环境
- **自动检测**：通过receipt路径检测TestFlight环境
- **使用生产API**：`https://bridal-api.wenhaofree.com`
- **完整功能**：支持真实的一次性支付和积分充值

### App Store环境
- **Release构建**：使用生产环境API
- **完整功能**：所有功能正常工作

## 🚀 发布流程建议

### 1. TestFlight发布前检查
```bash
# 确认Archive使用Release配置
# 检查日志输出确认API地址
# 测试一次性支付功能
```

### 2. 发布步骤
1. **清理构建**：`Product → Clean Build Folder`
2. **Archive构建**：`Product → Archive`
3. **检查配置**：确认使用Release配置
4. **上传TestFlight**：通过Xcode或Transporter
5. **功能测试**：在TestFlight中测试API调用

### 3. 验证清单
- [ ] Archive使用Release配置
- [ ] TestFlight版本显示正确的API地址日志
- [ ] 一次性支付功能正常工作
- [ ] 网络请求指向生产环境
- [ ] 积分充值接口调用成功

## 🔧 故障排除

### 如果TestFlight仍使用开发IP
1. **重新Archive**：确保使用Release配置
2. **检查日志**：查看环境检测日志
3. **清理缓存**：删除DerivedData文件夹
4. **重新上传**：重新构建并上传TestFlight

### 如果环境检测失败
1. **检查Bundle配置**：确认appStoreReceiptURL可访问
2. **添加调试日志**：增加更多环境检测日志
3. **手动配置**：考虑使用配置文件方式

## 📊 修改的文件

1. **`Sources/CommonUI/Constants.swift`**
   - 添加智能环境判断逻辑
   - 实现TestFlight检测函数
   - 优化API地址选择逻辑

2. **`Sources/SubscriptionCore/SubscriptionCore.swift`**
   - 修改一次性充值接口使用动态API地址
   - 添加CommonUI依赖以使用APIEndpoints

## ✅ 预期效果

修复后的行为：
- **开发时**：使用本地/局域网开发服务器
- **TestFlight**：自动使用生产环境API
- **App Store**：使用生产环境API
- **日志清晰**：明确显示当前使用的环境和API地址

这样确保TestFlight版本能够正确调用生产环境的积分充值接口。
