#!/usr/bin/env swift

import Foundation

// 模拟Apple登录响应数据
let mockAppleLoginResponse = """
{
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************.gNOIbrBxMtS77OKinjQi_FSmzDxTRfuNB9dh-Z5Meh8",
    "token_type": "bearer",
    "user": {
        "email": "<EMAIL>",
        "is_active": true,
        "is_superuser": false,
        "full_name": "test haha",
        "platform": "ios",
        "created_at": "2025-07-31T13:27:16.502514",
        "last_login": "2025-08-01T02:09:00.650981",
        "auth_provider": "apple",
        "provider_user_id": "001031.ed50d3aba6e14fa5a08019de106260ae.0506",
        "avatar_url": null,
        "id": "9c509938-e996-4551-80d2-c074cb4148d4"
    },
    "is_new_user": false,
    "trial_status": null
}
"""

// 模拟数据结构
struct AppleOAuthResponse: Codable {
    let accessToken: String
    let tokenType: String
    let user: AppleOAuthUser
    let isNewUser: Bool
    let trialStatus: String?
    
    private enum CodingKeys: String, CodingKey {
        case accessToken = "access_token"
        case tokenType = "token_type"
        case user = "user"
        case isNewUser = "is_new_user"
        case trialStatus = "trial_status"
    }
}

struct AppleOAuthUser: Codable {
    let id: String
    let email: String
    let isActive: Bool
    let isSuperuser: Bool
    let fullName: String
    let platform: String
    let createdAt: String
    let lastLogin: String
    let authProvider: String
    let providerUserId: String
    let avatarUrl: String?
    
    private enum CodingKeys: String, CodingKey {
        case id = "id"
        case email = "email"
        case isActive = "is_active"
        case isSuperuser = "is_superuser"
        case fullName = "full_name"
        case platform = "platform"
        case createdAt = "created_at"
        case lastLogin = "last_login"
        case authProvider = "auth_provider"
        case providerUserId = "provider_user_id"
        case avatarUrl = "avatar_url"
    }
}

// 模拟AccessTokenManager
struct AccessTokenManager {
    static func storeTokens(accessToken: String, tokenType: String) {
        // 存储到UserDefaults
        UserDefaults.standard.set(accessToken, forKey: "apple_access_token")
        UserDefaults.standard.set(tokenType, forKey: "apple_token_type")
        
        // 存储到Keychain
        let keychainQuery: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: "auth_token",
            kSecValueData as String: accessToken.data(using: .utf8)!
        ]
        
        // 先删除旧的token
        SecItemDelete(keychainQuery as CFDictionary)

        // 添加新的token
        let status = SecItemAdd(keychainQuery as CFDictionary, nil)
        print("🔐 Token存储状态:")
        print("   UserDefaults: ✅")
        print("   Keychain: \(status == errSecSuccess ? "✅" : "❌ (状态码: \(status))")")
    }
    
    static func getAppleAccessToken() -> String? {
        return UserDefaults.standard.string(forKey: "apple_access_token")
    }
    
    static func getKeychainAccessToken() -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: "auth_token",
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess,
           let data = result as? Data,
           let token = String(data: data, encoding: .utf8) {
            return token
        }
        
        return nil
    }
    
    static func getAuthorizationHeader() -> String? {
        if let token = getAppleAccessToken() {
            let tokenType = UserDefaults.standard.string(forKey: "apple_token_type") ?? "bearer"
            return "\(tokenType) \(token)"
        }
        
        if let token = getKeychainAccessToken() {
            return "bearer \(token)"
        }
        
        return nil
    }
    
    static func printStatus() {
        print("🔍 Access Token 状态检查:")
        
        if let appleToken = getAppleAccessToken() {
            print("   ✅ Apple Token (UserDefaults): \(appleToken.prefix(30))...")
        } else {
            print("   ❌ Apple Token (UserDefaults): 未找到")
        }
        
        if let keychainToken = getKeychainAccessToken() {
            print("   ✅ Keychain Token: \(keychainToken.prefix(30))...")
        } else {
            print("   ❌ Keychain Token: 未找到")
        }
        
        if let authHeader = getAuthorizationHeader() {
            print("   ✅ Authorization Header: \(authHeader.prefix(40))...")
        } else {
            print("   ❌ Authorization Header: 未找到")
        }
    }
}

// 模拟API请求
func makeAuthenticatedRequest(endpoint: String) {
    print("🌐 模拟API请求: \(endpoint)")
    
    guard let authHeader = AccessTokenManager.getAuthorizationHeader() else {
        print("❌ 无法获取认证头，请先登录")
        return
    }
    
    print("✅ 请求头构建成功:")
    print("   Authorization: \(authHeader)")
    print("   Content-Type: application/json")
    print("   Accept: application/json")
    print("📤 模拟发送请求到: \(endpoint)")
}

// 测试流程
func testAccessTokenFlow() {
    print("🧪 测试Access Token存储和使用流程")
    print(String(repeating: "=", count: 50))
    
    // 1. 模拟Apple登录成功，解析响应
    print("1️⃣ 模拟Apple登录成功，解析响应...")
    
    do {
        let responseData = mockAppleLoginResponse.data(using: .utf8)!
        let response = try JSONDecoder().decode(AppleOAuthResponse.self, from: responseData)
        
        print("✅ 登录响应解析成功:")
        print("   Access Token: \(response.accessToken.prefix(30))...")
        print("   Token Type: \(response.tokenType)")
        print("   用户ID: \(response.user.id)")
        print("   用户邮箱: \(response.user.email)")
        print("   用户姓名: \(response.user.fullName)")
        print()
        
        // 2. 存储Token
        print("2️⃣ 存储Access Token...")
        AccessTokenManager.storeTokens(
            accessToken: response.accessToken,
            tokenType: response.tokenType
        )
        print()
        
        // 3. 验证Token存储
        print("3️⃣ 验证Token存储...")
        AccessTokenManager.printStatus()
        print()
        
        // 4. 模拟后续API调用
        print("4️⃣ 模拟后续API调用...")
        makeAuthenticatedRequest(endpoint: "https://bridal-api.wenhaofree.com/api/v1/user/profile")
        print()
        makeAuthenticatedRequest(endpoint: "https://bridal-api.wenhaofree.com/api/v1/images/generate")
        print()
        makeAuthenticatedRequest(endpoint: "https://bridal-api.wenhaofree.com/api/v1/user/quota")
        print()
        
        print("🎉 Access Token流程测试完成！")
        
    } catch {
        print("❌ 测试失败: \(error)")
    }
}

// 运行测试
testAccessTokenFlow()
